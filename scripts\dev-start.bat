@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 启动智能会议系统开发环境
echo ================================

:: 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请先安装 Node.js 18+
    pause
    exit /b 1
)

:: 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装 Python 3.9+
    pause
    exit /b 1
)

echo ✅ 环境检查通过

:: 检查是否已安装依赖
if not exist "frontend\node_modules" (
    echo 📦 安装前端依赖...
    cd frontend
    call npm install
    if errorlevel 1 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
    cd ..
)

if not exist "backend\node_modules" (
    echo 📦 安装后端依赖...
    cd backend
    call npm install
    if errorlevel 1 (
        echo ❌ 后端依赖安装失败
        pause
        exit /b 1
    )
    cd ..
)

if not exist "ai-service\venv" (
    echo 📦 创建Python虚拟环境...
    cd ai-service
    python -m venv venv
    call venv\Scripts\activate.bat
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ AI服务依赖安装失败
        pause
        exit /b 1
    )
    cd ..
)

echo.
echo 🎯 选择启动模式:
echo 1. 完整启动 (前端 + 后端 + AI服务)
echo 2. 仅前端 (适合UI开发)
echo 3. 仅后端 (适合API开发)
echo 4. 仅AI服务 (适合AI功能开发)
echo 5. 测试录音功能 (打开录音测试页面)
echo.
set /p choice="请选择 (1-5): "

if "%choice%"=="1" goto full_start
if "%choice%"=="2" goto frontend_only
if "%choice%"=="3" goto backend_only
if "%choice%"=="4" goto ai_only
if "%choice%"=="5" goto test_recording
goto invalid_choice

:full_start
echo 🚀 启动完整开发环境...
echo.
echo 📋 启动顺序:
echo 1. AI服务 (端口 8001)
echo 2. 后端服务 (端口 8000) 
echo 3. 前端服务 (端口 3000)
echo.
echo ⚠️  请确保MySQL和Redis服务已启动
echo.
pause

:: 启动AI服务
echo 🤖 启动AI服务...
start "AI Service" cmd /k "cd ai-service && venv\Scripts\activate && python main.py"
timeout /t 3 /nobreak >nul

:: 启动后端服务
echo 🔧 启动后端服务...
start "Backend Service" cmd /k "cd backend && npm run dev"
timeout /t 3 /nobreak >nul

:: 启动前端服务
echo 📱 启动前端服务...
start "Frontend Service" cmd /k "cd frontend && npm run dev"

echo.
echo ✅ 所有服务启动完成！
echo.
echo 📱 前端应用: http://localhost:3000
echo 🔧 后端API: http://localhost:8000
echo 🤖 AI服务: http://localhost:8001
echo 📚 API文档: http://localhost:8001/docs
echo.
goto end

:frontend_only
echo 📱 启动前端服务...
cd frontend
call npm run dev
goto end

:backend_only
echo 🔧 启动后端服务...
echo ⚠️  请确保MySQL和Redis服务已启动
cd backend
call npm run dev
goto end

:ai_only
echo 🤖 启动AI服务...
cd ai-service
call venv\Scripts\activate.bat
python main.py
goto end

:test_recording
echo 🎤 打开录音功能测试页面...
start "" "prototype\test-recording.html"
echo.
echo 📖 测试说明:
echo 1. 允许浏览器访问麦克风
echo 2. 点击"开始录音"按钮
echo 3. 说话测试录音质量
echo 4. 点击"停止录音"保存文件
echo.
goto end

:invalid_choice
echo ❌ 无效选择，请重新运行脚本
goto end

:end
echo.
echo 💡 提示:
echo - 按 Ctrl+C 停止服务
echo - 查看各服务窗口的日志信息
echo - 如有问题请查看 README.md
echo.
pause
