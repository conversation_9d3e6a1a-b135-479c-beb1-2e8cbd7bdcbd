import winston from 'winston';
import path from 'path';
import { config } from '../config/app.config';

// 创建日志目录
const logDir = path.dirname(config.log.file);

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}] ${message}`;
    
    // 添加元数据
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta)}`;
    }
    
    // 添加错误堆栈
    if (stack) {
      log += `\n${stack}`;
    }
    
    return log;
  })
);

// 控制台格式（开发环境）
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} ${level} ${message}`;
    
    // 在开发环境中显示简化的元数据
    if (config.env === 'development' && Object.keys(meta).length > 0) {
      const filteredMeta = { ...meta };
      delete filteredMeta.timestamp;
      delete filteredMeta.level;
      delete filteredMeta.message;
      
      if (Object.keys(filteredMeta).length > 0) {
        log += ` ${JSON.stringify(filteredMeta, null, 2)}`;
      }
    }
    
    return log;
  })
);

// 创建传输器
const transports: winston.transport[] = [
  // 控制台输出
  new winston.transports.Console({
    level: config.env === 'development' ? 'debug' : 'info',
    format: consoleFormat,
  }),
];

// 生产环境添加文件输出
if (config.env === 'production') {
  transports.push(
    // 所有日志
    new winston.transports.File({
      filename: config.log.file,
      level: config.log.level,
      format: logFormat,
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
    }),
    
    // 错误日志
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      format: logFormat,
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
    })
  );
}

// 创建logger实例
export const logger = winston.createLogger({
  level: config.log.level,
  format: logFormat,
  transports,
  // 处理未捕获的异常
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'exceptions.log'),
      format: logFormat,
    })
  ],
  // 处理未处理的Promise拒绝
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'rejections.log'),
      format: logFormat,
    })
  ],
});

// 开发环境不退出进程
if (config.env !== 'production') {
  logger.exitOnError = false;
}

// 扩展logger功能
export class Logger {
  private context: string;

  constructor(context: string = 'App') {
    this.context = context;
  }

  private formatMessage(message: string): string {
    return `[${this.context}] ${message}`;
  }

  debug(message: string, meta?: any) {
    logger.debug(this.formatMessage(message), meta);
  }

  info(message: string, meta?: any) {
    logger.info(this.formatMessage(message), meta);
  }

  warn(message: string, meta?: any) {
    logger.warn(this.formatMessage(message), meta);
  }

  error(message: string, error?: Error | any, meta?: any) {
    if (error instanceof Error) {
      logger.error(this.formatMessage(message), {
        error: error.message,
        stack: error.stack,
        ...meta,
      });
    } else {
      logger.error(this.formatMessage(message), { error, ...meta });
    }
  }

  // 性能日志
  performance(operation: string, duration: number, meta?: any) {
    logger.info(this.formatMessage(`Performance: ${operation}`), {
      duration: `${duration}ms`,
      ...meta,
    });
  }

  // 安全日志
  security(event: string, meta?: any) {
    logger.warn(this.formatMessage(`Security: ${event}`), {
      type: 'security',
      ...meta,
    });
  }

  // 业务日志
  business(event: string, meta?: any) {
    logger.info(this.formatMessage(`Business: ${event}`), {
      type: 'business',
      ...meta,
    });
  }
}

// 创建默认logger实例
export const appLogger = new Logger('App');
export const authLogger = new Logger('Auth');
export const meetingLogger = new Logger('Meeting');
export const aiLogger = new Logger('AI');

// 导出默认logger
export default logger;
