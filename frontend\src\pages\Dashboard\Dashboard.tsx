import React, { useEffect, useState } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Button,
  List,
  Avatar,
  Space,
  Typography,
  Progress,
  Tag,
  Empty,
  Spin,
} from 'antd';
import {
  SoundOutlined,
  CheckSquareOutlined,
  ClockCircleOutlined,
  UserOutlined,
  PlusOutlined,
  PlayCircleOutlined,
  FileTextOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useMeetingStore } from '@/stores/meetingStore';
import { useAuthStore } from '@/stores/authStore';

const { Title, Text, Paragraph } = Typography;

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { meetings, isLoading, fetchMeetings } = useMeetingStore();
  const [stats, setStats] = useState({
    totalMeetings: 0,
    completedMeetings: 0,
    pendingTasks: 0,
    totalDuration: 0,
  });

  // 获取数据
  useEffect(() => {
    fetchMeetings(1, 10); // 获取最近10个会议
  }, [fetchMeetings]);

  // 计算统计数据
  useEffect(() => {
    if (meetings.length > 0) {
      const completed = meetings.filter(m => m.status === 'COMPLETED').length;
      const totalDuration = meetings.reduce((sum, m) => sum + (m.duration || 0), 0);
      const pendingTasks = meetings.reduce((sum, m) => 
        sum + m.tasks.filter(t => t.status === 'PENDING').length, 0
      );

      setStats({
        totalMeetings: meetings.length,
        completedMeetings: completed,
        pendingTasks,
        totalDuration: Math.round(totalDuration / 60), // 转换为分钟
      });
    }
  }, [meetings]);

  // 最近会议列表
  const recentMeetings = meetings.slice(0, 5);

  // 待办任务列表
  const pendingTasks = meetings
    .flatMap(m => m.tasks.filter(t => t.status === 'PENDING'))
    .slice(0, 5);

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'TRANSCRIBING':
        return 'processing';
      case 'PENDING':
        return 'warning';
      default:
        return 'default';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return '已完成';
      case 'TRANSCRIBING':
        return '转写中';
      case 'PENDING':
        return '待处理';
      default:
        return status;
    }
  };

  // 格式化时间
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    }
    return `${minutes}分钟`;
  };

  return (
    <div>
      {/* 欢迎信息 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          👋 欢迎回来，{user?.name}！
        </Title>
        <Paragraph type="secondary">
          今天是 {new Date().toLocaleDateString('zh-CN', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            weekday: 'long'
          })}
        </Paragraph>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总会议数"
              value={stats.totalMeetings}
              prefix={<SoundOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已完成会议"
              value={stats.completedMeetings}
              prefix={<CheckSquareOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="待办任务"
              value={stats.pendingTasks}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总时长"
              value={stats.totalDuration}
              suffix="分钟"
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card 
        title="快速操作" 
        style={{ marginBottom: 24 }}
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => navigate('/meetings/create')}
          >
            创建会议
          </Button>
        }
      >
        <Row gutter={16}>
          <Col xs={24} sm={8}>
            <Card 
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => navigate('/meetings/create')}
            >
              <SoundOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 16 }} />
              <Title level={4}>开始录音</Title>
              <Text type="secondary">创建新会议并开始录音</Text>
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card 
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => navigate('/meetings')}
            >
              <FileTextOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 16 }} />
              <Title level={4}>查看会议</Title>
              <Text type="secondary">浏览和管理所有会议</Text>
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card 
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => navigate('/tasks')}
            >
              <CheckSquareOutlined style={{ fontSize: 32, color: '#faad14', marginBottom: 16 }} />
              <Title level={4}>任务管理</Title>
              <Text type="secondary">查看和处理待办任务</Text>
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 最近会议和待办任务 */}
      <Row gutter={[16, 16]}>
        {/* 最近会议 */}
        <Col xs={24} lg={12}>
          <Card 
            title="最近会议" 
            extra={
              <Button 
                type="link" 
                onClick={() => navigate('/meetings')}
              >
                查看全部
              </Button>
            }
          >
            <Spin spinning={isLoading}>
              {recentMeetings.length > 0 ? (
                <List
                  dataSource={recentMeetings}
                  renderItem={(meeting) => (
                    <List.Item
                      actions={[
                        <Button 
                          type="link" 
                          icon={<PlayCircleOutlined />}
                          onClick={() => navigate(`/meetings/${meeting.id}`)}
                        >
                          查看
                        </Button>
                      ]}
                    >
                      <List.Item.Meta
                        avatar={
                          <Avatar 
                            icon={<SoundOutlined />} 
                            style={{ backgroundColor: '#1890ff' }}
                          />
                        }
                        title={
                          <Space>
                            <span>{meeting.title}</span>
                            <Tag color={getStatusColor(meeting.status)}>
                              {getStatusText(meeting.status)}
                            </Tag>
                          </Space>
                        }
                        description={
                          <Space direction="vertical" size={4}>
                            <Text type="secondary">
                              {new Date(meeting.date).toLocaleDateString()}
                            </Text>
                            {meeting.duration && (
                              <Text type="secondary">
                                时长: {formatDuration(meeting.duration)}
                              </Text>
                            )}
                            {meeting.customerName && (
                              <Text type="secondary">
                                客户: {meeting.customerName}
                              </Text>
                            )}
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              ) : (
                <Empty 
                  description="暂无会议记录"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )}
            </Spin>
          </Card>
        </Col>

        {/* 待办任务 */}
        <Col xs={24} lg={12}>
          <Card 
            title="待办任务" 
            extra={
              <Button 
                type="link" 
                onClick={() => navigate('/tasks')}
              >
                查看全部
              </Button>
            }
          >
            {pendingTasks.length > 0 ? (
              <List
                dataSource={pendingTasks}
                renderItem={(task) => (
                  <List.Item
                    actions={[
                      <Button 
                        type="link" 
                        onClick={() => navigate(`/meetings/${task.meetingId}`)}
                      >
                        查看
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <Avatar 
                          icon={<CheckSquareOutlined />} 
                          style={{ backgroundColor: '#faad14' }}
                        />
                      }
                      title={task.title}
                      description={
                        <Space direction="vertical" size={4}>
                          {task.description && (
                            <Text type="secondary">{task.description}</Text>
                          )}
                          {task.dueDate && (
                            <Text type="secondary">
                              截止: {new Date(task.dueDate).toLocaleDateString()}
                            </Text>
                          )}
                          {task.assigneeName && (
                            <Text type="secondary">
                              负责人: {task.assigneeName}
                            </Text>
                          )}
                          {task.progress > 0 && (
                            <Progress 
                              percent={task.progress} 
                              size="small" 
                              style={{ width: 200 }}
                            />
                          )}
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <Empty 
                description="暂无待办任务"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
