import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service';
import { logger } from '../utils/logger';

// 扩展Request类型以包含用户信息
declare global {
  namespace Express {
    interface Request {
      user?: {
        userId: number;
        email: string;
        role: string;
      };
    }
  }
}

const authService = new AuthService();

// 认证中间件
export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: '缺少认证token',
      });
    }

    const token = authHeader.substring(7); // 移除 "Bearer " 前缀

    try {
      const payload = authService.verifyToken(token);
      
      // 验证用户是否存在且活跃
      const user = await authService.findUserById(payload.userId);
      if (!user || !user.isActive) {
        return res.status(401).json({
          success: false,
          message: '用户不存在或已被禁用',
        });
      }

      // 将用户信息添加到请求对象
      req.user = {
        userId: payload.userId,
        email: payload.email,
        role: payload.role,
      };

      next();
    } catch (tokenError) {
      logger.warn(`Token验证失败: ${tokenError}`);
      return res.status(401).json({
        success: false,
        message: 'Token无效或已过期',
      });
    }
  } catch (error) {
    logger.error(`认证中间件错误: ${error}`);
    return res.status(500).json({
      success: false,
      message: '认证服务错误',
    });
  }
};

// 可选认证中间件（用户可能已登录也可能未登录）
export const optionalAuthMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      try {
        const payload = authService.verifyToken(token);
        const user = await authService.findUserById(payload.userId);
        
        if (user && user.isActive) {
          req.user = {
            userId: payload.userId,
            email: payload.email,
            role: payload.role,
          };
        }
      } catch (tokenError) {
        // 忽略token错误，继续处理请求
        logger.debug(`可选认证token无效: ${tokenError}`);
      }
    }

    next();
  } catch (error) {
    logger.error(`可选认证中间件错误: ${error}`);
    next(); // 继续处理请求
  }
};

// 角色检查中间件
export const requireRole = (requiredRole: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '需要认证',
      });
    }

    if (req.user.role !== requiredRole && req.user.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: '权限不足',
      });
    }

    next();
  };
};

// 管理员权限中间件
export const requireAdmin = requireRole('ADMIN');

// 资源所有者检查中间件
export const requireOwnership = (resourceIdParam: string = 'id') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '需要认证',
        });
      }

      // 管理员可以访问所有资源
      if (req.user.role === 'ADMIN') {
        return next();
      }

      const resourceId = req.params[resourceIdParam];
      if (!resourceId) {
        return res.status(400).json({
          success: false,
          message: '缺少资源ID',
        });
      }

      // 这里需要根据具体的资源类型来检查所有权
      // 例如，检查会议是否属于当前用户
      // 具体实现需要在各个控制器中处理

      next();
    } catch (error) {
      logger.error(`所有权检查错误: ${error}`);
      return res.status(500).json({
        success: false,
        message: '权限检查失败',
      });
    }
  };
};

// API密钥认证中间件（用于AI服务等内部调用）
export const apiKeyMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const apiKey = req.headers['x-api-key'] as string;
  const expectedApiKey = process.env.INTERNAL_API_KEY;

  if (!expectedApiKey) {
    logger.warn('未配置内部API密钥');
    return res.status(500).json({
      success: false,
      message: '服务配置错误',
    });
  }

  if (!apiKey || apiKey !== expectedApiKey) {
    logger.warn(`API密钥验证失败: ${req.ip}`);
    return res.status(401).json({
      success: false,
      message: 'API密钥无效',
    });
  }

  next();
};

// 请求日志中间件
export const requestLogMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  // 记录请求开始
  logger.info(`📥 ${req.method} ${req.url}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.userId,
  });

  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const logLevel = res.statusCode >= 400 ? 'warn' : 'info';
    
    logger[logLevel](`📤 ${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`, {
      ip: req.ip,
      userId: req.user?.userId,
      statusCode: res.statusCode,
      duration,
    });
  });

  next();
};
