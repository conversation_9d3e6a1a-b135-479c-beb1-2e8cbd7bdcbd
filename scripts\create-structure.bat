@echo off
echo Creating project directory structure...

REM Frontend directories
mkdir frontend\src\components\Layout 2>nul
mkdir frontend\src\components\AudioRecorder 2>nul
mkdir frontend\src\components\AudioPlayer 2>nul
mkdir frontend\src\components\Common 2>nul
mkdir frontend\src\pages\Login 2>nul
mkdir frontend\src\pages\Dashboard 2>nul
mkdir frontend\src\pages\Meeting 2>nul
mkdir frontend\src\pages\Tasks 2>nul
mkdir frontend\src\hooks 2>nul
mkdir frontend\src\services 2>nul
mkdir frontend\src\stores 2>nul
mkdir frontend\src\utils 2>nul
mkdir frontend\src\types 2>nul
mkdir frontend\src\test 2>nul
mkdir frontend\public 2>nul

REM Backend directories
mkdir backend\src\controllers 2>nul
mkdir backend\src\services 2>nul
mkdir backend\src\models 2>nul
mkdir backend\src\middleware 2>nul
mkdir backend\src\routes 2>nul
mkdir backend\src\utils 2>nul
mkdir backend\src\config 2>nul
mkdir backend\src\types 2>nul
mkdir backend\prisma\migrations 2>nul
mkdir backend\tests 2>nul

REM AI service directories
mkdir ai-service\src\models 2>nul
mkdir ai-service\src\services 2>nul
mkdir ai-service\src\api 2>nul
mkdir ai-service\src\utils 2>nul
mkdir ai-service\src\config 2>nul
mkdir ai-service\tests 2>nul

REM Other directories
mkdir models 2>nul
mkdir uploads 2>nul
mkdir temp 2>nul
mkdir logs 2>nul
mkdir nginx\ssl 2>nul
mkdir database 2>nul

echo Directory structure created successfully!
