# 🚀 智能会议系统快速开始指南

欢迎使用智能会议系统！本指南将帮助您快速搭建和运行整个系统。

## 📋 系统要求

### 必需环境
- **Node.js**: 18.0+ 
- **Python**: 3.9+
- **MySQL**: 8.0+
- **Redis**: 7.0+

### 可选环境
- **Docker**: 20.0+ (推荐使用Docker部署)
- **NVIDIA GPU**: 支持CUDA的显卡 (AI加速，可选)

### 硬件建议
- **CPU**: 4核心以上
- **内存**: 8GB以上 (16GB推荐)
- **存储**: 20GB可用空间
- **GPU**: NVIDIA GTX 1060 或更高 (可选)

## 🛠️ 安装步骤

### 方式一：自动安装 (推荐)

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd smart-meeting-system
   ```

2. **运行安装脚本**
   ```bash
   # Windows
   scripts\setup.bat
   
   # Linux/macOS
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   ```

3. **检查安装状态**
   ```bash
   # Windows
   scripts\check-status.bat
   
   # Linux/macOS
   ./scripts/check-status.sh
   ```

### 方式二：手动安装

#### 1. 环境配置

**复制环境配置文件**
```bash
cp .env.example .env
```

**编辑配置文件** (根据您的环境修改)
```bash
# 数据库配置
DATABASE_URL="mysql://meeting_user:meeting_pass@localhost:3306/meeting_db"
REDIS_URL="redis://localhost:6379"

# JWT密钥 (请修改为随机字符串)
JWT_SECRET="your-super-secret-jwt-key-change-in-production"

# AI服务配置
WHISPER_MODEL_SIZE="base"  # tiny, base, small, medium, large
```

#### 2. 安装依赖

**前端依赖**
```bash
cd frontend
npm install
cd ..
```

**后端依赖**
```bash
cd backend
npm install
npx prisma generate
cd ..
```

**AI服务依赖**
```bash
cd ai-service
python -m venv venv

# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

pip install -r requirements.txt
cd ..
```

#### 3. 数据库设置

**启动MySQL和Redis**
```bash
# 使用Docker (推荐)
docker run -d --name mysql -p 3306:3306 -e MYSQL_ROOT_PASSWORD=password mysql:8.0
docker run -d --name redis -p 6379:6379 redis:7-alpine

# 或使用本地安装的服务
# 确保MySQL和Redis服务正在运行
```

**执行数据库迁移**
```bash
cd backend
npx prisma migrate dev
npx prisma db seed  # 可选：插入测试数据
cd ..
```

## 🚀 启动系统

### 方式一：使用启动脚本 (推荐)

```bash
# Windows
scripts\dev-start.bat

# Linux/macOS
./scripts/dev-start.sh
```

选择启动模式：
1. **完整启动** - 启动所有服务
2. **仅前端** - 适合UI开发
3. **仅后端** - 适合API开发
4. **仅AI服务** - 适合AI功能开发
5. **测试录音** - 打开录音测试页面

### 方式二：手动启动

**启动AI服务** (终端1)
```bash
cd ai-service
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

python main.py
```

**启动后端服务** (终端2)
```bash
cd backend
npm run dev
```

**启动前端服务** (终端3)
```bash
cd frontend
npm run dev
```

## 🌐 访问系统

启动完成后，您可以通过以下地址访问系统：

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **AI服务**: http://localhost:8001
- **API文档**: http://localhost:8001/docs

### 默认登录账号

- **邮箱**: <EMAIL>
- **密码**: 123456

## 🧪 功能测试

### 1. 测试录音功能

1. 打开浏览器访问 `prototype/test-recording.html`
2. 允许浏览器访问麦克风
3. 点击"开始录音"按钮
4. 说话测试录音质量
5. 点击"停止录音"保存文件

### 2. 测试AI转写

1. 登录系统后进入"会议管理"
2. 点击"创建会议"
3. 填写会议信息并开始录音
4. 录音完成后系统会自动进行转写
5. 查看转写结果和智能纪要

### 3. 测试Whisper模型

```bash
cd prototype
python test-whisper.py --audio your-audio-file.wav --model base
```

## 🐳 Docker部署

### 使用Docker Compose (推荐)

```bash
# 构建镜像
docker-compose build

# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 单独构建镜像

```bash
# 前端
docker build -t meeting-frontend ./frontend

# 后端
docker build -t meeting-backend ./backend

# AI服务
docker build -t meeting-ai ./ai-service
```

## 🔧 常见问题

### Q1: 前端启动失败，提示端口被占用
**解决方案**: 
- 检查端口3000是否被占用：`netstat -an | findstr 3000`
- 修改前端端口：在 `frontend/vite.config.ts` 中修改端口配置

### Q2: AI服务启动失败，提示模型下载失败
**解决方案**:
- 检查网络连接
- 手动下载模型：`python -c "import whisper; whisper.load_model('base')"`
- 使用国内镜像源

### Q3: 数据库连接失败
**解决方案**:
- 检查MySQL服务是否启动
- 验证数据库连接配置
- 确保数据库用户权限正确

### Q4: 录音功能无法使用
**解决方案**:
- 使用HTTPS或localhost访问
- 检查浏览器麦克风权限
- 确保使用支持的浏览器 (Chrome, Firefox, Edge)

### Q5: GPU加速不工作
**解决方案**:
- 安装NVIDIA驱动和CUDA
- 安装PyTorch GPU版本：`pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118`
- 检查GPU可用性：`python -c "import torch; print(torch.cuda.is_available())"`

## 📊 性能优化

### 1. AI服务优化
- 使用GPU加速转写
- 调整并发处理数量
- 选择合适的模型大小

### 2. 数据库优化
- 创建适当的索引
- 定期清理过期数据
- 使用连接池

### 3. 前端优化
- 启用代码分割
- 使用CDN加速
- 压缩静态资源

## 🔒 安全配置

### 生产环境安全检查清单

- [ ] 修改默认JWT密钥
- [ ] 设置强密码策略
- [ ] 启用HTTPS
- [ ] 配置防火墙规则
- [ ] 定期备份数据
- [ ] 监控系统日志
- [ ] 更新依赖版本

## 📚 更多资源

- **技术文档**: `docs/技术设计文档.md`
- **开发计划**: `docs/开发计划.md`
- **API文档**: http://localhost:8001/docs
- **问题反馈**: 提交Issue到项目仓库

## 🤝 获取帮助

如果您在使用过程中遇到问题：

1. 查看本指南的常见问题部分
2. 检查系统日志文件
3. 运行状态检查脚本
4. 提交Issue到项目仓库
5. 联系技术支持团队

---

🎉 **恭喜！您已成功搭建智能会议系统！**

现在您可以开始使用系统的各项功能，包括实时录音、语音转写、智能纪要生成等。祝您使用愉快！
