@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 📊 智能会议系统状态检查
echo ========================

:: 检查系统要求
echo.
echo 🔍 检查系统要求...
echo ----------------

:: 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js: 未安装
    set node_status=❌
) else (
    for /f "tokens=*" %%i in ('node --version') do set node_version=%%i
    echo ✅ Node.js: !node_version!
    set node_status=✅
)

:: 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python: 未安装
    set python_status=❌
) else (
    for /f "tokens=*" %%i in ('python --version') do set python_version=%%i
    echo ✅ Python: !python_version!
    set python_status=✅
)

:: 检查Git
git --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Git: 未安装
    set git_status=❌
) else (
    for /f "tokens=*" %%i in ('git --version') do set git_version=%%i
    echo ✅ Git: !git_version!
    set git_status=✅
)

:: 检查Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Docker: 未安装 (可选)
    set docker_status=⚠️
) else (
    for /f "tokens=*" %%i in ('docker --version') do set docker_version=%%i
    echo ✅ Docker: !docker_version!
    set docker_status=✅
)

:: 检查项目结构
echo.
echo 📁 检查项目结构...
echo ----------------

if exist "frontend" (
    echo ✅ frontend/ 目录存在
    set frontend_dir=✅
) else (
    echo ❌ frontend/ 目录不存在
    set frontend_dir=❌
)

if exist "backend" (
    echo ✅ backend/ 目录存在
    set backend_dir=✅
) else (
    echo ❌ backend/ 目录不存在
    set backend_dir=❌
)

if exist "ai-service" (
    echo ✅ ai-service/ 目录存在
    set ai_dir=✅
) else (
    echo ❌ ai-service/ 目录不存在
    set ai_dir=❌
)

if exist "docs" (
    echo ✅ docs/ 目录存在
    set docs_dir=✅
) else (
    echo ❌ docs/ 目录不存在
    set docs_dir=❌
)

:: 检查配置文件
echo.
echo ⚙️  检查配置文件...
echo ----------------

if exist ".env" (
    echo ✅ .env 配置文件存在
    set env_file=✅
) else (
    echo ⚠️  .env 配置文件不存在 (将使用默认配置)
    set env_file=⚠️
)

if exist "docker-compose.yml" (
    echo ✅ docker-compose.yml 存在
    set compose_file=✅
) else (
    echo ❌ docker-compose.yml 不存在
    set compose_file=❌
)

if exist "package.json" (
    echo ✅ 根目录 package.json 存在
    set root_package=✅
) else (
    echo ❌ 根目录 package.json 不存在
    set root_package=❌
)

:: 检查依赖安装
echo.
echo 📦 检查依赖安装...
echo ----------------

if exist "frontend\node_modules" (
    echo ✅ 前端依赖已安装
    set frontend_deps=✅
) else (
    echo ❌ 前端依赖未安装
    set frontend_deps=❌
)

if exist "backend\node_modules" (
    echo ✅ 后端依赖已安装
    set backend_deps=✅
) else (
    echo ❌ 后端依赖未安装
    set backend_deps=❌
)

if exist "ai-service\venv" (
    echo ✅ AI服务虚拟环境已创建
    set ai_venv=✅
) else (
    echo ❌ AI服务虚拟环境未创建
    set ai_venv=❌
)

:: 检查端口占用
echo.
echo 🌐 检查端口占用...
echo ----------------

netstat -an | find "3000" | find "LISTENING" >nul 2>&1
if errorlevel 1 (
    echo ✅ 端口 3000 (前端) 可用
    set port_3000=✅
) else (
    echo ⚠️  端口 3000 (前端) 被占用
    set port_3000=⚠️
)

netstat -an | find "8000" | find "LISTENING" >nul 2>&1
if errorlevel 1 (
    echo ✅ 端口 8000 (后端) 可用
    set port_8000=✅
) else (
    echo ⚠️  端口 8000 (后端) 被占用
    set port_8000=⚠️
)

netstat -an | find "8001" | find "LISTENING" >nul 2>&1
if errorlevel 1 (
    echo ✅ 端口 8001 (AI服务) 可用
    set port_8001=✅
) else (
    echo ⚠️  端口 8001 (AI服务) 被占用
    set port_8001=⚠️
)

:: 检查数据库服务
echo.
echo 🗄️  检查数据库服务...
echo ------------------

netstat -an | find "3306" | find "LISTENING" >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL (端口 3306) 未启动
    set mysql_status=❌
) else (
    echo ✅ MySQL (端口 3306) 正在运行
    set mysql_status=✅
)

netstat -an | find "6379" | find "LISTENING" >nul 2>&1
if errorlevel 1 (
    echo ❌ Redis (端口 6379) 未启动
    set redis_status=❌
) else (
    echo ✅ Redis (端口 6379) 正在运行
    set redis_status=✅
)

:: 生成状态报告
echo.
echo 📋 状态报告
echo ==========
echo.
echo 系统要求:
echo   Node.js:     !node_status!
echo   Python:      !python_status!
echo   Git:         !git_status!
echo   Docker:      !docker_status!
echo.
echo 项目结构:
echo   frontend/:   !frontend_dir!
echo   backend/:    !backend_dir!
echo   ai-service/: !ai_dir!
echo   docs/:       !docs_dir!
echo.
echo 配置文件:
echo   .env:                !env_file!
echo   docker-compose.yml:  !compose_file!
echo   package.json:        !root_package!
echo.
echo 依赖安装:
echo   前端依赖:    !frontend_deps!
echo   后端依赖:    !backend_deps!
echo   AI虚拟环境:  !ai_venv!
echo.
echo 端口状态:
echo   3000 (前端):   !port_3000!
echo   8000 (后端):   !port_8000!
echo   8001 (AI服务): !port_8001!
echo.
echo 数据库服务:
echo   MySQL:  !mysql_status!
echo   Redis:  !redis_status!
echo.

:: 给出建议
echo 💡 建议操作:
echo -----------

if "!frontend_deps!"=="❌" (
    echo - 运行 "cd frontend && npm install" 安装前端依赖
)

if "!backend_deps!"=="❌" (
    echo - 运行 "cd backend && npm install" 安装后端依赖
)

if "!ai_venv!"=="❌" (
    echo - 运行 "cd ai-service && python -m venv venv && venv\Scripts\activate && pip install -r requirements.txt"
)

if "!env_file!"=="⚠️" (
    echo - 复制 .env.example 为 .env 并配置相关参数
)

if "!mysql_status!"=="❌" (
    echo - 启动 MySQL 服务或使用 Docker: docker run -d -p 3306:3306 -e MYSQL_ROOT_PASSWORD=password mysql:8.0
)

if "!redis_status!"=="❌" (
    echo - 启动 Redis 服务或使用 Docker: docker run -d -p 6379:6379 redis:7-alpine
)

echo.
echo 🚀 准备就绪后，运行 "scripts\dev-start.bat" 启动开发环境
echo.
pause
