import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { PrismaClient, User } from '@prisma/client';
import { config } from '../config/app.config';

const prisma = new PrismaClient();

export interface CreateUserData {
  email: string;
  password: string;
  name: string;
  phone?: string;
  department?: string;
}

export interface UpdateUserData {
  name?: string;
  phone?: string;
  department?: string;
}

export interface JWTPayload {
  userId: number;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

export class AuthService {
  // 创建用户
  async createUser(data: CreateUserData): Promise<User> {
    const { email, password, name, phone, department } = data;

    // 加密密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const user = await prisma.user.create({
      data: {
        email,
        passwordHash,
        name,
        phone,
        department,
      },
    });

    return user;
  }

  // 根据邮箱查找用户
  async findUserByEmail(email: string): Promise<User | null> {
    return prisma.user.findUnique({
      where: { email },
    });
  }

  // 根据ID查找用户
  async findUserById(id: number): Promise<User | null> {
    return prisma.user.findUnique({
      where: { id },
    });
  }

  // 验证用户凭据
  async validateUser(email: string, password: string): Promise<User | null> {
    const user = await this.findUserByEmail(email);
    if (!user) {
      return null;
    }

    const isValidPassword = await this.validatePassword(password, user.passwordHash);
    if (!isValidPassword) {
      return null;
    }

    return user;
  }

  // 验证密码
  async validatePassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  // 生成JWT token
  generateToken(user: User, expiresIn: string = '7d'): string {
    const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
      userId: user.id,
      email: user.email,
      role: user.role,
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn,
      issuer: 'smart-meeting-system',
      audience: 'smart-meeting-users',
    });
  }

  // 验证JWT token
  verifyToken(token: string): JWTPayload {
    try {
      return jwt.verify(token, config.jwt.secret, {
        issuer: 'smart-meeting-system',
        audience: 'smart-meeting-users',
      }) as JWTPayload;
    } catch (error) {
      throw new Error('Invalid token');
    }
  }

  // 更新用户信息
  async updateUser(id: number, data: UpdateUserData): Promise<User> {
    return prisma.user.update({
      where: { id },
      data,
    });
  }

  // 更新密码
  async updatePassword(id: number, newPassword: string): Promise<void> {
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(newPassword, saltRounds);

    await prisma.user.update({
      where: { id },
      data: { passwordHash },
    });
  }

  // 禁用/启用用户
  async toggleUserStatus(id: number, isActive: boolean): Promise<User> {
    return prisma.user.update({
      where: { id },
      data: { isActive },
    });
  }

  // 获取用户列表（管理员功能）
  async getUsers(page: number = 1, pageSize: number = 20) {
    const skip = (page - 1) * pageSize;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        skip,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          email: true,
          name: true,
          phone: true,
          department: true,
          role: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      }),
      prisma.user.count(),
    ]);

    return {
      items: users,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  // 删除用户（软删除）
  async deleteUser(id: number): Promise<void> {
    await prisma.user.update({
      where: { id },
      data: { isActive: false },
    });
  }

  // 检查用户权限
  async checkPermission(userId: number, permission: string): Promise<boolean> {
    const user = await this.findUserById(userId);
    if (!user || !user.isActive) {
      return false;
    }

    // 管理员拥有所有权限
    if (user.role === 'ADMIN') {
      return true;
    }

    // 这里可以扩展更复杂的权限检查逻辑
    // 目前简单实现：普通用户只能访问自己的数据
    return permission === 'read_own' || permission === 'write_own';
  }

  // 生成密码重置token
  generateResetToken(user: User): string {
    const payload = {
      userId: user.id,
      email: user.email,
      type: 'password_reset',
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: '1h', // 1小时有效期
      issuer: 'smart-meeting-system',
      audience: 'password-reset',
    });
  }

  // 验证密码重置token
  verifyResetToken(token: string): { userId: number; email: string } {
    try {
      const payload = jwt.verify(token, config.jwt.secret, {
        issuer: 'smart-meeting-system',
        audience: 'password-reset',
      }) as any;

      if (payload.type !== 'password_reset') {
        throw new Error('Invalid token type');
      }

      return {
        userId: payload.userId,
        email: payload.email,
      };
    } catch (error) {
      throw new Error('Invalid or expired reset token');
    }
  }

  // 重置密码
  async resetPassword(token: string, newPassword: string): Promise<void> {
    const { userId } = this.verifyResetToken(token);
    
    const user = await this.findUserById(userId);
    if (!user || !user.isActive) {
      throw new Error('User not found or inactive');
    }

    await this.updatePassword(userId, newPassword);
  }
}
