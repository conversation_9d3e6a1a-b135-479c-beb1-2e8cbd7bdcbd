import { Request, Response, NextFunction } from 'express';
import { MeetingService } from '../services/meeting.service';
import { TranscriptionService } from '../services/transcription.service';
import { logger } from '../utils/logger';
import { 
  validateCreateMeetingRequest, 
  validateUpdateMeetingRequest,
  validatePaginationParams 
} from '../utils/validation';
import { uploadAudioFile } from '../utils/fileUpload';

export class MeetingController {
  private meetingService: MeetingService;
  private transcriptionService: TranscriptionService;

  constructor() {
    this.meetingService = new MeetingService();
    this.transcriptionService = new TranscriptionService();
  }

  // 获取会议列表
  getMeetings = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未认证',
        });
      }

      // 验证分页参数
      const { error: paginationError, value: paginationParams } = validatePaginationParams(req.query);
      if (paginationError) {
        return res.status(400).json({
          success: false,
          message: '分页参数错误',
          errors: paginationError.details.map(detail => detail.message),
        });
      }

      const { page, pageSize } = paginationParams;
      
      // 构建筛选条件
      const filters: any = {};
      if (req.query.status) filters.status = req.query.status;
      if (req.query.customerName) filters.customerName = req.query.customerName;
      if (req.query.startDate && req.query.endDate) {
        filters.dateRange = {
          start: new Date(req.query.startDate as string),
          end: new Date(req.query.endDate as string),
        };
      }
      if (req.query.tags) {
        filters.tags = Array.isArray(req.query.tags) ? req.query.tags : [req.query.tags];
      }

      const result = await this.meetingService.getMeetings(userId, page, pageSize, filters);

      res.json({
        success: true,
        message: '获取会议列表成功',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };

  // 获取单个会议详情
  getMeetingById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.userId;
      const meetingId = parseInt(req.params.id);

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未认证',
        });
      }

      if (isNaN(meetingId)) {
        return res.status(400).json({
          success: false,
          message: '无效的会议ID',
        });
      }

      const meeting = await this.meetingService.getMeetingById(meetingId, userId);

      if (!meeting) {
        return res.status(404).json({
          success: false,
          message: '会议不存在',
        });
      }

      res.json({
        success: true,
        message: '获取会议详情成功',
        data: meeting,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };

  // 创建会议
  createMeeting = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未认证',
        });
      }

      const { error, value } = validateCreateMeetingRequest(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: error.details.map(detail => detail.message),
        });
      }

      const meetingData = {
        ...value,
        creatorId: userId,
      };

      const meeting = await this.meetingService.createMeeting(meetingData);

      logger.info(`用户 ${userId} 创建会议: ${meeting.id}`);

      res.status(201).json({
        success: true,
        message: '创建会议成功',
        data: meeting,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };

  // 更新会议
  updateMeeting = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.userId;
      const meetingId = parseInt(req.params.id);

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未认证',
        });
      }

      if (isNaN(meetingId)) {
        return res.status(400).json({
          success: false,
          message: '无效的会议ID',
        });
      }

      const { error, value } = validateUpdateMeetingRequest(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: error.details.map(detail => detail.message),
        });
      }

      // 检查会议是否存在且用户有权限
      const existingMeeting = await this.meetingService.getMeetingById(meetingId, userId);
      if (!existingMeeting) {
        return res.status(404).json({
          success: false,
          message: '会议不存在或无权限访问',
        });
      }

      const updatedMeeting = await this.meetingService.updateMeeting(meetingId, value);

      logger.info(`用户 ${userId} 更新会议: ${meetingId}`);

      res.json({
        success: true,
        message: '更新会议成功',
        data: updatedMeeting,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };

  // 删除会议
  deleteMeeting = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.userId;
      const meetingId = parseInt(req.params.id);

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未认证',
        });
      }

      if (isNaN(meetingId)) {
        return res.status(400).json({
          success: false,
          message: '无效的会议ID',
        });
      }

      // 检查会议是否存在且用户有权限
      const existingMeeting = await this.meetingService.getMeetingById(meetingId, userId);
      if (!existingMeeting) {
        return res.status(404).json({
          success: false,
          message: '会议不存在或无权限访问',
        });
      }

      await this.meetingService.deleteMeeting(meetingId);

      logger.info(`用户 ${userId} 删除会议: ${meetingId}`);

      res.json({
        success: true,
        message: '删除会议成功',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };

  // 上传音频文件
  uploadAudio = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.userId;
      const meetingId = parseInt(req.params.id);

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未认证',
        });
      }

      if (isNaN(meetingId)) {
        return res.status(400).json({
          success: false,
          message: '无效的会议ID',
        });
      }

      // 检查会议是否存在且用户有权限
      const existingMeeting = await this.meetingService.getMeetingById(meetingId, userId);
      if (!existingMeeting) {
        return res.status(404).json({
          success: false,
          message: '会议不存在或无权限访问',
        });
      }

      // 处理文件上传
      const uploadResult = await uploadAudioFile(req, res);
      if (!uploadResult.success) {
        return res.status(400).json(uploadResult);
      }

      const { filePath, fileSize, originalName } = uploadResult.data;

      // 更新会议信息
      const updatedMeeting = await this.meetingService.updateMeeting(meetingId, {
        audioFilePath: filePath,
        audioFileSize: fileSize,
        status: 'TRANSCRIBING',
      });

      // 启动转写任务
      this.transcriptionService.startTranscription(meetingId, filePath)
        .catch(error => {
          logger.error(`转写任务启动失败: ${error}`);
        });

      logger.info(`用户 ${userId} 上传音频文件到会议 ${meetingId}: ${originalName}`);

      res.json({
        success: true,
        message: '音频上传成功，开始转写处理',
        data: updatedMeeting,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };

  // 获取转写状态
  getTranscriptionStatus = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.userId;
      const meetingId = parseInt(req.params.id);

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未认证',
        });
      }

      if (isNaN(meetingId)) {
        return res.status(400).json({
          success: false,
          message: '无效的会议ID',
        });
      }

      const status = await this.transcriptionService.getTranscriptionStatus(meetingId);

      res.json({
        success: true,
        message: '获取转写状态成功',
        data: status,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };

  // 下载音频文件
  downloadAudio = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.userId;
      const meetingId = parseInt(req.params.id);

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未认证',
        });
      }

      if (isNaN(meetingId)) {
        return res.status(400).json({
          success: false,
          message: '无效的会议ID',
        });
      }

      // 检查会议是否存在且用户有权限
      const meeting = await this.meetingService.getMeetingById(meetingId, userId);
      if (!meeting || !meeting.audioFilePath) {
        return res.status(404).json({
          success: false,
          message: '音频文件不存在',
        });
      }

      // 设置下载响应头
      res.setHeader('Content-Disposition', `attachment; filename="meeting-${meetingId}-audio.wav"`);
      res.setHeader('Content-Type', 'audio/wav');

      // 发送文件
      res.sendFile(meeting.audioFilePath, (error) => {
        if (error) {
          logger.error(`音频文件下载失败: ${error}`);
          if (!res.headersSent) {
            res.status(500).json({
              success: false,
              message: '文件下载失败',
            });
          }
        } else {
          logger.info(`用户 ${userId} 下载会议 ${meetingId} 的音频文件`);
        }
      });
    } catch (error) {
      next(error);
    }
  };
}
