import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config();

export const config = {
  // 应用配置
  env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT || '8000', 10),
  
  // 数据库配置
  database: {
    url: process.env.DATABASE_URL || 'mysql://meeting_user:meeting_pass@localhost:3306/meeting_db',
  },
  
  // Redis配置
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
  },
  
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  },
  
  // 文件上传配置
  upload: {
    dir: process.env.UPLOAD_DIR || path.join(process.cwd(), 'uploads'),
    maxSize: parseInt(process.env.MAX_FILE_SIZE || '524288000', 10), // 500MB
    allowedTypes: ['audio/wav', 'audio/mp3', 'audio/m4a', 'audio/aac', 'audio/flac'],
  },
  
  // CORS配置
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  },
  
  // 速率限制配置
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15分钟
    max: parseInt(process.env.RATE_LIMIT_MAX || '100', 10), // 每个窗口期最大请求数
  },
  
  // AI服务配置
  aiService: {
    url: process.env.AI_SERVICE_URL || 'http://localhost:8001',
    timeout: 300000, // 5分钟超时
  },
  
  // 邮件服务配置
  email: {
    host: process.env.SMTP_HOST || '',
    port: parseInt(process.env.SMTP_PORT || '587', 10),
    user: process.env.SMTP_USER || '',
    pass: process.env.SMTP_PASS || '',
    from: process.env.SMTP_FROM || '',
  },
  
  // 日志配置
  log: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || path.join(process.cwd(), 'logs', 'app.log'),
  },
  
  // 性能配置
  performance: {
    workerProcesses: parseInt(process.env.WORKER_PROCESSES || '1', 10),
    maxConcurrentTranscriptions: parseInt(process.env.MAX_CONCURRENT_TRANSCRIPTIONS || '3', 10),
  },
};
