@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 智能会议系统安装脚本 (Windows)
echo ==================================

:: 检查Node.js
echo 📋 检查系统要求...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请先安装 Node.js 18+
    pause
    exit /b 1
)

for /f "tokens=1 delims=." %%a in ('node --version') do (
    set node_major=%%a
    set node_major=!node_major:v=!
)

if !node_major! LSS 18 (
    echo ❌ Node.js 版本过低，需要 18+
    pause
    exit /b 1
)

echo ✅ Node.js 版本检查通过

:: 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装 Python 3.9+
    pause
    exit /b 1
)

echo ✅ Python 版本检查通过

:: 检查Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Docker 未安装，将使用手动安装模式
    set USE_DOCKER=false
) else (
    echo ✅ Docker 可用
    set USE_DOCKER=true
)

:: 创建环境配置
echo ⚙️  配置环境变量...
if not exist .env (
    copy .env.example .env >nul
    echo ✅ 已创建 .env 文件
) else (
    echo ✅ .env 文件已存在
)

:: 选择安装方式
if "%USE_DOCKER%"=="true" (
    echo.
    echo 选择安装方式:
    echo 1. Docker 安装 (推荐)
    echo 2. 手动安装
    set /p choice="请选择 (1 或 2): "
    
    if "!choice!"=="1" (
        goto docker_install
    ) else (
        goto manual_install
    )
) else (
    goto manual_install
)

:docker_install
echo 🐳 使用Docker安装...

:: 检查docker-compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose 未安装
    pause
    exit /b 1
)

echo 📦 构建Docker镜像...
docker-compose build

echo 🚀 启动基础服务...
docker-compose up -d mysql redis

echo ⏳ 等待数据库启动...
timeout /t 15 /nobreak >nul

echo 🗄️  执行数据库迁移...
docker-compose run --rm backend npm run db:migrate

echo 🚀 启动所有服务...
docker-compose up -d

echo ✅ Docker安装完成！
goto show_info

:manual_install
echo 🔧 手动安装模式...

echo 📦 安装前端依赖...
cd frontend
call npm install
if errorlevel 1 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
)
cd ..

echo 📦 安装后端依赖...
cd backend
call npm install
if errorlevel 1 (
    echo ❌ 后端依赖安装失败
    pause
    exit /b 1
)

echo 🗄️  生成数据库客户端...
call npx prisma generate
cd ..

echo 🤖 安装AI服务依赖...
cd ai-service

:: 创建虚拟环境
if not exist venv (
    python -m venv venv
    echo ✅ 已创建Python虚拟环境
)

:: 激活虚拟环境并安装依赖
call venv\Scripts\activate.bat
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ AI服务依赖安装失败
    pause
    exit /b 1
)

echo 📥 下载Whisper模型...
python -c "import whisper; whisper.load_model('base')"

cd ..

echo ✅ 手动安装完成！

:show_info
echo.
echo 🎉 安装完成！
echo ===============
echo 📱 前端应用: http://localhost:3000
echo 🔧 后端API: http://localhost:8000
echo 🤖 AI服务: http://localhost:8001
echo 🗄️  数据库管理: npm run db:studio
echo.
echo 📚 更多信息请查看 README.md
echo.

if "%USE_DOCKER%"=="true" (
    echo 🐳 Docker命令:
    echo   查看日志: docker-compose logs -f
    echo   停止服务: docker-compose down
    echo   重启服务: docker-compose restart
    echo.
    echo 📊 当前服务状态:
    docker-compose ps
) else (
    echo 🚀 启动开发服务:
    echo   请在不同命令行窗口中运行以下命令:
    echo   1. 后端服务: cd backend ^&^& npm run dev
    echo   2. AI服务: cd ai-service ^&^& venv\Scripts\activate ^&^& python main.py
    echo   3. 前端服务: cd frontend ^&^& npm run dev
    echo.
    echo   注意: 请确保MySQL和Redis服务已启动
)

echo.
echo 按任意键退出...
pause >nul
