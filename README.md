# 智能会议系统 (Smart Meeting System)

一个基于AI的智能会议管理系统，支持PC端实时录音、语音转写、说话人识别和智能纪要生成。

## ✨ 主要功能

- 🎤 **PC端实时录音**: 浏览器内一键录音，实时质量监控
- 🗣️ **多人识别**: 自动区分说话人，统计发言时长和活跃度  
- 📝 **智能纪要**: AI自动生成结构化会议纪要
- 🔍 **智能搜索**: 全文搜索，支持语音搜索和多维度筛选
- 📋 **任务管理**: 自动提取任务，跟踪执行进度
- 🤖 **本地AI**: 使用免费开源Whisper模型，零API调用成本

## 🏗️ 技术架构

- **前端**: React 18 + TypeScript + Vite + Ant Design
- **后端**: Node.js + Express + TypeScript + Prisma
- **AI服务**: Python + FastAPI + Whisper + pyannote-audio
- **数据库**: MySQL 8.0 + Redis
- **部署**: Docker + Docker Compose + Nginx

## 🚀 快速开始

### 环境要求

- Node.js 18+
- Python 3.9+
- Docker & Docker Compose
- MySQL 8.0
- Redis 7.0
- NVIDIA GPU (可选，用于AI加速)

### 1. 克隆项目

```bash
git clone <repository-url>
cd smart-meeting-system
```

### 2. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 3. 使用Docker启动（推荐）

```bash
# 构建并启动所有服务
npm run docker:build
npm run docker:up

# 查看日志
npm run docker:logs
```

### 4. 手动安装启动

```bash
# 安装依赖
npm run setup

# 数据库迁移
npm run db:migrate

# 启动开发服务
npm run dev
```

## 📁 项目结构

```
smart-meeting-system/
├── frontend/              # React前端应用
│   ├── src/
│   │   ├── components/    # 通用组件
│   │   ├── pages/         # 页面组件
│   │   ├── hooks/         # 自定义Hooks
│   │   ├── services/      # API服务
│   │   ├── stores/        # 状态管理
│   │   └── utils/         # 工具函数
│   ├── package.json
│   └── vite.config.ts
├── backend/               # Node.js后端服务
│   ├── src/
│   │   ├── controllers/   # 控制器
│   │   ├── services/      # 业务逻辑
│   │   ├── models/        # 数据模型
│   │   ├── middleware/    # 中间件
│   │   ├── routes/        # 路由
│   │   └── utils/         # 工具函数
│   ├── prisma/           # 数据库模式
│   └── package.json
├── ai-service/           # Python AI服务
│   ├── src/
│   │   ├── models/       # AI模型管理
│   │   ├── services/     # AI服务
│   │   ├── api/          # API接口
│   │   └── utils/        # 工具函数
│   ├── requirements.txt
│   └── main.py
├── nginx/                # Nginx配置
├── docs/                 # 文档
├── docker-compose.yml    # Docker编排
└── package.json          # 根项目配置
```

## 🔧 开发指南

### 前端开发

```bash
cd frontend
npm run dev      # 启动开发服务器
npm run build    # 构建生产版本
npm run test     # 运行测试
npm run lint     # 代码检查
```

### 后端开发

```bash
cd backend
npm run dev      # 启动开发服务器
npm run build    # 构建TypeScript
npm run test     # 运行测试
npm run db:studio # 数据库管理界面
```

### AI服务开发

```bash
cd ai-service
python main.py   # 启动AI服务
pytest          # 运行测试
```

## 📊 API文档

启动服务后访问：
- 后端API文档: http://localhost:8000/api-docs
- AI服务文档: http://localhost:8001/docs

## 🧪 测试

```bash
# 运行所有测试
npm run test

# 运行特定服务测试
npm run test:frontend
npm run test:backend
npm run test:ai
```

## 📦 部署

### 生产环境部署

```bash
# 1. 构建生产镜像
docker-compose -f docker-compose.prod.yml build

# 2. 启动生产服务
docker-compose -f docker-compose.prod.yml up -d

# 3. 数据库迁移
docker-compose exec backend npm run db:deploy
```

### 性能优化

- 启用GPU加速（需要NVIDIA GPU）
- 配置Redis缓存
- 使用CDN加速静态资源
- 启用Nginx压缩

## 🔒 安全配置

- 修改默认密码和密钥
- 配置HTTPS证书
- 设置防火墙规则
- 启用访问日志

## 📈 监控

- 应用性能监控
- 错误日志收集
- 资源使用监控
- 业务指标统计

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
- 提交 [Issue](issues)
- 发送邮件至 <EMAIL>
- 查看 [文档](docs/)

## 🙏 致谢

- [OpenAI Whisper](https://github.com/openai/whisper) - 语音识别模型
- [pyannote-audio](https://github.com/pyannote/pyannote-audio) - 说话人分离
- [React](https://reactjs.org/) - 前端框架
- [FastAPI](https://fastapi.tiangolo.com/) - Python Web框架
