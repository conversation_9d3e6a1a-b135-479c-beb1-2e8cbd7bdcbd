"""
AI服务配置文件
"""

import os
from pathlib import Path
from typing import List

class Settings:
    """应用配置类"""
    
    # 基础配置
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    PORT: int = int(os.getenv("PORT", "8001"))
    
    # 路径配置
    BASE_DIR: Path = Path(__file__).parent.parent.parent
    MODEL_CACHE_DIR: Path = Path(os.getenv("MODEL_CACHE_DIR", BASE_DIR / "models"))
    AUDIO_TEMP_DIR: Path = Path(os.getenv("AUDIO_TEMP_DIR", BASE_DIR / "temp"))
    UPLOAD_DIR: Path = Path(os.getenv("UPLOAD_DIR", BASE_DIR / "uploads"))
    
    # Whisper模型配置
    WHISPER_MODEL_SIZE: str = os.getenv("WHISPER_MODEL_SIZE", "base")  # tiny, base, small, medium, large, large-v3
    WHISPER_LANGUAGE: str = os.getenv("WHISPER_LANGUAGE", "zh")
    WHISPER_DEVICE: str = os.getenv("WHISPER_DEVICE", "auto")  # auto, cpu, cuda
    WHISPER_COMPUTE_TYPE: str = os.getenv("WHISPER_COMPUTE_TYPE", "float16")  # float16, float32, int8
    
    # 说话人分离配置
    DIARIZATION_MODEL: str = os.getenv("DIARIZATION_MODEL", "pyannote/speaker-diarization-3.1")
    DIARIZATION_MIN_SPEAKERS: int = int(os.getenv("DIARIZATION_MIN_SPEAKERS", "1"))
    DIARIZATION_MAX_SPEAKERS: int = int(os.getenv("DIARIZATION_MAX_SPEAKERS", "8"))
    
    # 音频处理配置
    AUDIO_SAMPLE_RATE: int = int(os.getenv("AUDIO_SAMPLE_RATE", "16000"))
    AUDIO_CHANNELS: int = int(os.getenv("AUDIO_CHANNELS", "1"))
    MAX_AUDIO_DURATION: int = int(os.getenv("MAX_AUDIO_DURATION", "14400"))  # 4小时
    
    # 性能配置
    MAX_CONCURRENT_TASKS: int = int(os.getenv("MAX_CONCURRENT_TASKS", "3"))
    TASK_TIMEOUT: int = int(os.getenv("TASK_TIMEOUT", "3600"))  # 1小时
    
    # GPU配置
    CUDA_VISIBLE_DEVICES: str = os.getenv("CUDA_VISIBLE_DEVICES", "0")
    
    # 支持的音频格式
    SUPPORTED_AUDIO_FORMATS: List[str] = [
        "audio/wav", "audio/mp3", "audio/m4a", 
        "audio/aac", "audio/flac", "audio/ogg"
    ]
    
    # 支持的音频文件扩展名
    SUPPORTED_AUDIO_EXTENSIONS: List[str] = [
        ".wav", ".mp3", ".m4a", ".aac", ".flac", ".ogg"
    ]
    
    # 热词配置
    MAX_HOTWORDS_PER_USER: int = int(os.getenv("MAX_HOTWORDS_PER_USER", "500"))
    MAX_SYSTEM_HOTWORDS: int = int(os.getenv("MAX_SYSTEM_HOTWORDS", "1000"))
    
    # 缓存配置
    ENABLE_MODEL_CACHE: bool = os.getenv("ENABLE_MODEL_CACHE", "true").lower() == "true"
    CACHE_TTL: int = int(os.getenv("CACHE_TTL", "3600"))  # 1小时
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "logs/ai_service.log")
    
    def __init__(self):
        """初始化配置，创建必要的目录"""
        self.MODEL_CACHE_DIR.mkdir(parents=True, exist_ok=True)
        self.AUDIO_TEMP_DIR.mkdir(parents=True, exist_ok=True)
        self.UPLOAD_DIR.mkdir(parents=True, exist_ok=True)
        
        # 创建日志目录
        log_dir = Path(self.LOG_FILE).parent
        log_dir.mkdir(parents=True, exist_ok=True)
    
    @property
    def whisper_config(self) -> dict:
        """获取Whisper配置"""
        return {
            "model_size": self.WHISPER_MODEL_SIZE,
            "language": self.WHISPER_LANGUAGE,
            "device": self.WHISPER_DEVICE,
            "compute_type": self.WHISPER_COMPUTE_TYPE,
        }
    
    @property
    def diarization_config(self) -> dict:
        """获取说话人分离配置"""
        return {
            "model": self.DIARIZATION_MODEL,
            "min_speakers": self.DIARIZATION_MIN_SPEAKERS,
            "max_speakers": self.DIARIZATION_MAX_SPEAKERS,
        }
    
    @property
    def audio_config(self) -> dict:
        """获取音频处理配置"""
        return {
            "sample_rate": self.AUDIO_SAMPLE_RATE,
            "channels": self.AUDIO_CHANNELS,
            "max_duration": self.MAX_AUDIO_DURATION,
        }

# 创建全局配置实例
settings = Settings()
