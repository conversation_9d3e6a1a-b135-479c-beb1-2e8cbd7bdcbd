# 环境配置示例文件
# 复制此文件为 .env 并修改相应配置

# 应用环境
NODE_ENV=development

# 数据库配置
DATABASE_URL="mysql://meeting_user:meeting_pass@localhost:3306/meeting_db"

# Redis配置
REDIS_URL="redis://localhost:6379"

# JWT配置
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="7d"

# 文件上传配置
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=524288000  # 500MB

# AI服务配置
AI_SERVICE_URL="http://localhost:8001"

# 邮件服务配置（可选）
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
SMTP_FROM=""

# 前端配置
REACT_APP_API_URL="http://localhost:8000"
REACT_APP_WS_URL="ws://localhost:8000"
REACT_APP_AI_URL="http://localhost:8001"

# AI模型配置
WHISPER_MODEL_SIZE="base"  # tiny, base, small, medium, large, large-v3
MODEL_CACHE_DIR="./models"
AUDIO_TEMP_DIR="./temp"

# 日志配置
LOG_LEVEL="info"
LOG_FILE="./logs/app.log"

# 安全配置
CORS_ORIGIN="http://localhost:3000"
RATE_LIMIT_WINDOW_MS=900000  # 15分钟
RATE_LIMIT_MAX=100  # 每个窗口期最大请求数

# 性能配置
WORKER_PROCESSES=1
MAX_CONCURRENT_TRANSCRIPTIONS=3
