#!/usr/bin/env node
/**
 * 系统监控脚本
 * 监控系统资源使用情况、服务状态和性能指标
 */

const os = require('os');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class SystemMonitor {
  constructor() {
    this.metrics = {
      timestamp: new Date().toISOString(),
      system: {},
      services: {},
      performance: {},
      alerts: []
    };
    
    this.thresholds = {
      cpu: 80,        // CPU使用率阈值 (%)
      memory: 85,     // 内存使用率阈值 (%)
      disk: 90,       // 磁盘使用率阈值 (%)
      responseTime: 2000,  // 响应时间阈值 (ms)
      errorRate: 5    // 错误率阈值 (%)
    };
  }

  /**
   * 获取系统信息
   */
  getSystemMetrics() {
    const cpus = os.cpus();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;
    
    // CPU使用率计算
    let totalIdle = 0;
    let totalTick = 0;
    
    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    });
    
    const idle = totalIdle / cpus.length;
    const total = totalTick / cpus.length;
    const cpuUsage = 100 - ~~(100 * idle / total);
    
    this.metrics.system = {
      platform: os.platform(),
      arch: os.arch(),
      hostname: os.hostname(),
      uptime: os.uptime(),
      loadavg: os.loadavg(),
      cpu: {
        count: cpus.length,
        model: cpus[0].model,
        usage: cpuUsage
      },
      memory: {
        total: totalMem,
        used: usedMem,
        free: freeMem,
        usage: Math.round((usedMem / totalMem) * 100)
      }
    };
    
    // 检查阈值
    if (cpuUsage > this.thresholds.cpu) {
      this.addAlert('high_cpu', `CPU使用率过高: ${cpuUsage}%`);
    }
    
    if (this.metrics.system.memory.usage > this.thresholds.memory) {
      this.addAlert('high_memory', `内存使用率过高: ${this.metrics.system.memory.usage}%`);
    }
  }

  /**
   * 获取磁盘使用情况
   */
  getDiskMetrics() {
    try {
      let diskUsage = {};
      
      if (os.platform() === 'win32') {
        // Windows
        const output = execSync('wmic logicaldisk get size,freespace,caption', { encoding: 'utf8' });
        const lines = output.split('\n').filter(line => line.trim());
        
        for (let i = 1; i < lines.length; i++) {
          const parts = lines[i].trim().split(/\s+/);
          if (parts.length >= 3) {
            const drive = parts[0];
            const free = parseInt(parts[1]);
            const total = parseInt(parts[2]);
            const used = total - free;
            const usage = Math.round((used / total) * 100);
            
            diskUsage[drive] = {
              total,
              used,
              free,
              usage
            };
            
            if (usage > this.thresholds.disk) {
              this.addAlert('high_disk', `磁盘 ${drive} 使用率过高: ${usage}%`);
            }
          }
        }
      } else {
        // Linux/macOS
        const output = execSync('df -h', { encoding: 'utf8' });
        const lines = output.split('\n').filter(line => line.startsWith('/'));
        
        lines.forEach(line => {
          const parts = line.split(/\s+/);
          if (parts.length >= 6) {
            const filesystem = parts[0];
            const usage = parseInt(parts[4].replace('%', ''));
            
            diskUsage[filesystem] = {
              size: parts[1],
              used: parts[2],
              available: parts[3],
              usage
            };
            
            if (usage > this.thresholds.disk) {
              this.addAlert('high_disk', `磁盘 ${filesystem} 使用率过高: ${usage}%`);
            }
          }
        });
      }
      
      this.metrics.system.disk = diskUsage;
    } catch (error) {
      console.warn('获取磁盘信息失败:', error.message);
    }
  }

  /**
   * 检查服务状态
   */
  async checkServices() {
    const services = [
      { name: 'frontend', url: 'http://localhost:3000', port: 3000 },
      { name: 'backend', url: 'http://localhost:8000/health', port: 8000 },
      { name: 'ai-service', url: 'http://localhost:8001/health', port: 8001 },
      { name: 'mysql', port: 3306 },
      { name: 'redis', port: 6379 }
    ];

    for (const service of services) {
      try {
        const status = await this.checkServiceStatus(service);
        this.metrics.services[service.name] = status;
        
        if (!status.running) {
          this.addAlert('service_down', `服务 ${service.name} 未运行`);
        }
        
        if (status.responseTime && status.responseTime > this.thresholds.responseTime) {
          this.addAlert('slow_response', `服务 ${service.name} 响应缓慢: ${status.responseTime}ms`);
        }
      } catch (error) {
        this.metrics.services[service.name] = {
          running: false,
          error: error.message
        };
        this.addAlert('service_error', `服务 ${service.name} 检查失败: ${error.message}`);
      }
    }
  }

  /**
   * 检查单个服务状态
   */
  async checkServiceStatus(service) {
    const startTime = Date.now();
    
    // 检查端口是否开放
    const portOpen = await this.checkPort(service.port);
    
    if (!portOpen) {
      return {
        running: false,
        port: service.port,
        error: 'Port not open'
      };
    }
    
    // 如果有URL，检查HTTP响应
    if (service.url) {
      try {
        const response = await fetch(service.url, {
          timeout: 5000
        });
        
        const responseTime = Date.now() - startTime;
        
        return {
          running: true,
          port: service.port,
          httpStatus: response.status,
          responseTime,
          healthy: response.ok
        };
      } catch (error) {
        return {
          running: true,
          port: service.port,
          httpError: error.message,
          responseTime: Date.now() - startTime
        };
      }
    }
    
    return {
      running: true,
      port: service.port
    };
  }

  /**
   * 检查端口是否开放
   */
  checkPort(port) {
    return new Promise((resolve) => {
      const net = require('net');
      const socket = new net.Socket();
      
      socket.setTimeout(1000);
      
      socket.on('connect', () => {
        socket.destroy();
        resolve(true);
      });
      
      socket.on('timeout', () => {
        socket.destroy();
        resolve(false);
      });
      
      socket.on('error', () => {
        resolve(false);
      });
      
      socket.connect(port, 'localhost');
    });
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics() {
    try {
      // 检查日志文件大小
      const logFiles = [
        './logs/app.log',
        './logs/error.log',
        './logs/access.log'
      ];
      
      const logs = {};
      logFiles.forEach(logFile => {
        if (fs.existsSync(logFile)) {
          const stats = fs.statSync(logFile);
          logs[path.basename(logFile)] = {
            size: stats.size,
            modified: stats.mtime
          };
        }
      });
      
      // 检查上传目录大小
      let uploadsSize = 0;
      if (fs.existsSync('./uploads')) {
        uploadsSize = this.getDirectorySize('./uploads');
      }
      
      // 检查临时文件
      let tempSize = 0;
      if (fs.existsSync('./temp')) {
        tempSize = this.getDirectorySize('./temp');
      }
      
      this.metrics.performance = {
        logs,
        storage: {
          uploads: uploadsSize,
          temp: tempSize
        },
        processes: this.getProcessInfo()
      };
    } catch (error) {
      console.warn('获取性能指标失败:', error.message);
    }
  }

  /**
   * 获取目录大小
   */
  getDirectorySize(dirPath) {
    let totalSize = 0;
    
    try {
      const files = fs.readdirSync(dirPath);
      
      files.forEach(file => {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          totalSize += this.getDirectorySize(filePath);
        } else {
          totalSize += stats.size;
        }
      });
    } catch (error) {
      // 忽略权限错误等
    }
    
    return totalSize;
  }

  /**
   * 获取进程信息
   */
  getProcessInfo() {
    try {
      const processes = [];
      
      if (os.platform() === 'win32') {
        // Windows
        const output = execSync('tasklist /fo csv | findstr "node.exe,python.exe"', { encoding: 'utf8' });
        // 解析输出...
      } else {
        // Linux/macOS
        const output = execSync('ps aux | grep -E "(node|python)" | grep -v grep', { encoding: 'utf8' });
        const lines = output.split('\n').filter(line => line.trim());
        
        lines.forEach(line => {
          const parts = line.split(/\s+/);
          if (parts.length >= 11) {
            processes.push({
              pid: parts[1],
              cpu: parseFloat(parts[2]),
              memory: parseFloat(parts[3]),
              command: parts.slice(10).join(' ')
            });
          }
        });
      }
      
      return processes;
    } catch (error) {
      return [];
    }
  }

  /**
   * 添加告警
   */
  addAlert(type, message) {
    this.metrics.alerts.push({
      type,
      message,
      timestamp: new Date().toISOString(),
      severity: this.getAlertSeverity(type)
    });
  }

  /**
   * 获取告警严重程度
   */
  getAlertSeverity(type) {
    const severityMap = {
      high_cpu: 'warning',
      high_memory: 'warning',
      high_disk: 'critical',
      service_down: 'critical',
      service_error: 'error',
      slow_response: 'warning'
    };
    
    return severityMap[type] || 'info';
  }

  /**
   * 格式化字节大小
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 生成监控报告
   */
  generateReport() {
    console.log('📊 系统监控报告');
    console.log('='.repeat(50));
    console.log(`⏰ 时间: ${new Date().toLocaleString()}`);
    console.log('');
    
    // 系统信息
    console.log('💻 系统信息:');
    console.log(`  平台: ${this.metrics.system.platform} ${this.metrics.system.arch}`);
    console.log(`  主机: ${this.metrics.system.hostname}`);
    console.log(`  运行时间: ${Math.floor(this.metrics.system.uptime / 3600)}小时`);
    console.log(`  CPU: ${this.metrics.system.cpu.usage}% (${this.metrics.system.cpu.count}核)`);
    console.log(`  内存: ${this.metrics.system.memory.usage}% (${this.formatBytes(this.metrics.system.memory.used)}/${this.formatBytes(this.metrics.system.memory.total)})`);
    console.log('');
    
    // 服务状态
    console.log('🔧 服务状态:');
    Object.entries(this.metrics.services).forEach(([name, status]) => {
      const icon = status.running ? '✅' : '❌';
      const responseTime = status.responseTime ? ` (${status.responseTime}ms)` : '';
      console.log(`  ${icon} ${name}${responseTime}`);
    });
    console.log('');
    
    // 告警信息
    if (this.metrics.alerts.length > 0) {
      console.log('⚠️  告警信息:');
      this.metrics.alerts.forEach(alert => {
        const icon = alert.severity === 'critical' ? '🚨' : alert.severity === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`  ${icon} ${alert.message}`);
      });
      console.log('');
    } else {
      console.log('✅ 无告警信息');
      console.log('');
    }
    
    // 性能指标
    if (this.metrics.performance.storage) {
      console.log('📈 存储使用:');
      console.log(`  上传文件: ${this.formatBytes(this.metrics.performance.storage.uploads)}`);
      console.log(`  临时文件: ${this.formatBytes(this.metrics.performance.storage.temp)}`);
      console.log('');
    }
  }

  /**
   * 保存监控数据
   */
  saveMetrics() {
    const metricsDir = './logs/metrics';
    if (!fs.existsSync(metricsDir)) {
      fs.mkdirSync(metricsDir, { recursive: true });
    }
    
    const filename = `metrics-${new Date().toISOString().split('T')[0]}.json`;
    const filepath = path.join(metricsDir, filename);
    
    // 读取现有数据
    let existingData = [];
    if (fs.existsSync(filepath)) {
      try {
        existingData = JSON.parse(fs.readFileSync(filepath, 'utf8'));
      } catch (error) {
        // 忽略解析错误
      }
    }
    
    // 添加新数据
    existingData.push(this.metrics);
    
    // 保存数据
    fs.writeFileSync(filepath, JSON.stringify(existingData, null, 2));
  }

  /**
   * 运行监控
   */
  async run() {
    console.log('🔍 开始系统监控...');
    
    try {
      this.getSystemMetrics();
      this.getDiskMetrics();
      await this.checkServices();
      this.getPerformanceMetrics();
      
      this.generateReport();
      this.saveMetrics();
      
      return this.metrics;
    } catch (error) {
      console.error('💥 监控执行失败:', error.message);
      throw error;
    }
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
使用方法: node monitor.js [选项]

选项:
  -h, --help     显示帮助信息
  --json         以JSON格式输出
  --watch        持续监控模式
  --interval     监控间隔(秒，默认60)
  --save         保存监控数据到文件

示例:
  node monitor.js                    # 单次监控
  node monitor.js --json             # JSON输出
  node monitor.js --watch            # 持续监控
  node monitor.js --watch --interval 30  # 30秒间隔
`);
    return;
  }
  
  const monitor = new SystemMonitor();
  
  if (args.includes('--watch')) {
    // 持续监控模式
    const intervalIndex = args.indexOf('--interval');
    const interval = intervalIndex !== -1 ? parseInt(args[intervalIndex + 1]) * 1000 : 60000;
    
    console.log(`🔄 启动持续监控模式 (间隔: ${interval/1000}秒)`);
    console.log('按 Ctrl+C 停止监控\n');
    
    const runMonitor = async () => {
      try {
        await monitor.run();
        console.log('\n' + '='.repeat(50) + '\n');
      } catch (error) {
        console.error('监控执行失败:', error.message);
      }
    };
    
    // 立即执行一次
    await runMonitor();
    
    // 定时执行
    const timer = setInterval(runMonitor, interval);
    
    // 优雅退出
    process.on('SIGINT', () => {
      console.log('\n\n👋 停止监控');
      clearInterval(timer);
      process.exit(0);
    });
  } else {
    // 单次监控
    const metrics = await monitor.run();
    
    if (args.includes('--json')) {
      console.log(JSON.stringify(metrics, null, 2));
    }
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('💥 监控脚本执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = SystemMonitor;
