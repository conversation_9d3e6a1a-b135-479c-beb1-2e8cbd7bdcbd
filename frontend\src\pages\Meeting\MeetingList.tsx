import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Statistic,
  Avatar,
  Tooltip,
  Dropdown,
  Modal,
  message,
  Empty,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  PlayCircleOutlined,
  EditOutlined,
  DeleteOutlined,
  DownloadOutlined,
  SoundOutlined,
  UserOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useMeetingStore } from '@/stores/meetingStore';
import { Meeting } from '@/types/api';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

const MeetingList: React.FC = () => {
  const navigate = useNavigate();
  const {
    meetings,
    isLoading,
    pagination,
    filters,
    fetchMeetings,
    deleteMeeting,
    setFilters,
  } = useMeetingStore();

  const [searchText, setSearchText] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 获取会议列表
  useEffect(() => {
    fetchMeetings();
  }, [fetchMeetings]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
    setFilters({ ...filters, title: value });
    fetchMeetings(1, pagination.pageSize);
  };

  // 处理筛选
  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    fetchMeetings(1, pagination.pageSize);
  };

  // 处理分页
  const handleTableChange = (page: number, pageSize: number) => {
    fetchMeetings(page, pageSize);
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这个会议吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteMeeting(id);
          message.success('删除成功');
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的会议');
      return;
    }

    Modal.confirm({
      title: '批量删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个会议吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          for (const id of selectedRowKeys) {
            await deleteMeeting(id as number);
          }
          setSelectedRowKeys([]);
          message.success('批量删除成功');
        } catch (error) {
          message.error('批量删除失败');
        }
      },
    });
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig = {
      TRANSCRIBING: { color: 'processing', text: '转写中' },
      PENDING: { color: 'warning', text: '待处理' },
      COMPLETED: { color: 'success', text: '已完成' },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 格式化时长
  const formatDuration = (seconds?: number) => {
    if (!seconds) return '-';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  // 表格列配置
  const columns: ColumnsType<Meeting> = [
    {
      title: '会议标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      render: (text, record) => (
        <Space>
          <Avatar size="small" icon={<SoundOutlined />} style={{ backgroundColor: '#1890ff' }} />
          <a onClick={() => navigate(`/meetings/${record.id}`)}>{text}</a>
        </Space>
      ),
    },
    {
      title: '客户',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 120,
      render: (text) => text || '-',
    },
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date) => (
        <Space direction="vertical" size={0}>
          <span>{dayjs(date).format('YYYY-MM-DD')}</span>
          <span style={{ fontSize: 12, color: '#999' }}>
            {dayjs(date).format('HH:mm')}
          </span>
        </Space>
      ),
    },
    {
      title: '时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 80,
      render: formatDuration,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: getStatusTag,
    },
    {
      title: '参与人数',
      dataIndex: 'speakers',
      key: 'speakers',
      width: 100,
      render: (speakers) => (
        <Space>
          <UserOutlined />
          <span>{speakers?.length || 0}</span>
        </Space>
      ),
    },
    {
      title: '任务数',
      dataIndex: 'tasks',
      key: 'tasks',
      width: 80,
      render: (tasks) => (
        <Space>
          <span>{tasks?.length || 0}</span>
          {tasks?.some((task: any) => task.status === 'PENDING') && (
            <Tag color="orange" size="small">待办</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => {
        const menuItems = [
          {
            key: 'view',
            icon: <PlayCircleOutlined />,
            label: '查看详情',
            onClick: () => navigate(`/meetings/${record.id}`),
          },
          {
            key: 'edit',
            icon: <EditOutlined />,
            label: '编辑',
            onClick: () => navigate(`/meetings/${record.id}/edit`),
          },
          {
            key: 'download',
            icon: <DownloadOutlined />,
            label: '下载录音',
            disabled: !record.audioFilePath,
            onClick: () => {
              // 实现下载功能
              message.info('下载功能开发中');
            },
          },
          {
            type: 'divider' as const,
          },
          {
            key: 'delete',
            icon: <DeleteOutlined />,
            label: '删除',
            danger: true,
            onClick: () => handleDelete(record.id),
          },
        ];

        return (
          <Dropdown menu={{ items: menuItems }} trigger={['click']}>
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        );
      },
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  // 统计数据
  const stats = {
    total: meetings.length,
    completed: meetings.filter(m => m.status === 'COMPLETED').length,
    transcribing: meetings.filter(m => m.status === 'TRANSCRIBING').length,
    totalDuration: meetings.reduce((sum, m) => sum + (m.duration || 0), 0),
  };

  return (
    <div>
      {/* 页面标题和操作 */}
      <div style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <h2 style={{ margin: 0 }}>会议管理</h2>
          </Col>
          <Col>
            <Space>
              {selectedRowKeys.length > 0 && (
                <Button danger onClick={handleBatchDelete}>
                  批量删除 ({selectedRowKeys.length})
                </Button>
              )}
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => navigate('/meetings/create')}
              >
                创建会议
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总会议数"
              value={stats.total}
              prefix={<SoundOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completed}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="转写中"
              value={stats.transcribing}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总时长"
              value={formatDuration(stats.totalDuration)}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Input.Search
              placeholder="搜索会议标题、客户名称..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={handleSearch}
            />
          </Col>
          <Col>
            <Select
              placeholder="状态"
              allowClear
              style={{ width: 120 }}
              onChange={(value) => handleFilterChange('status', value)}
            >
              <Option value="TRANSCRIBING">转写中</Option>
              <Option value="PENDING">待处理</Option>
              <Option value="COMPLETED">已完成</Option>
            </Select>
          </Col>
          <Col>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              onChange={(dates) => {
                if (dates) {
                  handleFilterChange('dateRange', [
                    dates[0]?.toISOString(),
                    dates[1]?.toISOString(),
                  ]);
                } else {
                  handleFilterChange('dateRange', null);
                }
              }}
            />
          </Col>
          <Col>
            <Tooltip title="高级筛选">
              <Button icon={<FilterOutlined />} />
            </Tooltip>
          </Col>
        </Row>
      </Card>

      {/* 会议列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={meetings}
          rowKey="id"
          loading={isLoading}
          rowSelection={rowSelection}
          pagination={{
            current: pagination.page,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: handleTableChange,
          }}
          scroll={{ x: 1200 }}
          locale={{
            emptyText: (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无会议记录"
              >
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => navigate('/meetings/create')}
                >
                  创建第一个会议
                </Button>
              </Empty>
            ),
          }}
        />
      </Card>
    </div>
  );
};

export default MeetingList;
