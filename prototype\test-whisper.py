#!/usr/bin/env python3
"""
Whisper模型性能测试原型
用于验证本地Whisper模型的转写效果和性能
"""

import time
import os
import sys
import argparse
from pathlib import Path
import torch
import whisper
import librosa
import soundfile as sf
from typing import Dict, List, Optional

class WhisperTester:
    """Whisper模型测试器"""
    
    def __init__(self, model_size: str = "base"):
        """
        初始化测试器
        
        Args:
            model_size: 模型大小 (tiny, base, small, medium, large, large-v3)
        """
        self.model_size = model_size
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model = None
        
        print(f"🔧 初始化 Whisper 测试器")
        print(f"   模型大小: {model_size}")
        print(f"   设备: {self.device}")
        print(f"   GPU可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"   GPU: {gpu_name} ({gpu_memory:.1f}GB)")
    
    def load_model(self) -> None:
        """加载Whisper模型"""
        print(f"📦 加载 Whisper {self.model_size} 模型...")
        start_time = time.time()
        
        try:
            self.model = whisper.load_model(self.model_size, device=self.device)
            load_time = time.time() - start_time
            print(f"✅ 模型加载完成，耗时: {load_time:.2f}秒")
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            sys.exit(1)
    
    def test_audio_file(self, audio_path: str) -> Dict:
        """
        测试单个音频文件
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            测试结果字典
        """
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
        
        print(f"\n🎵 测试音频文件: {audio_path}")
        
        # 获取音频信息
        audio_info = self._get_audio_info(audio_path)
        print(f"   时长: {audio_info['duration']:.2f}秒")
        print(f"   采样率: {audio_info['sample_rate']}Hz")
        print(f"   声道数: {audio_info['channels']}")
        
        # 开始转写
        print("🔄 开始转写...")
        start_time = time.time()
        
        try:
            result = self.model.transcribe(
                audio_path,
                language="zh",
                task="transcribe",
                fp16=False if self.device == "cpu" else True,
                verbose=False
            )
            
            transcribe_time = time.time() - start_time
            
            # 计算性能指标
            speed_ratio = audio_info['duration'] / transcribe_time
            
            print(f"✅ 转写完成")
            print(f"   转写时间: {transcribe_time:.2f}秒")
            print(f"   速度比: {speed_ratio:.2f}x (音频时长/转写时间)")
            print(f"   语言: {result.get('language', 'unknown')}")
            
            # 显示转写结果
            text = result['text'].strip()
            print(f"\n📝 转写结果:")
            print(f"   {text}")
            
            # 显示片段信息
            segments = result.get('segments', [])
            print(f"\n📊 片段信息:")
            print(f"   片段数量: {len(segments)}")
            
            if segments:
                avg_confidence = sum(seg.get('avg_logprob', 0) for seg in segments) / len(segments)
                print(f"   平均置信度: {avg_confidence:.3f}")
                
                # 显示前3个片段
                print(f"   前3个片段:")
                for i, seg in enumerate(segments[:3]):
                    start = seg['start']
                    end = seg['end']
                    text = seg['text'].strip()
                    confidence = seg.get('avg_logprob', 0)
                    print(f"     {i+1}. [{start:.1f}s-{end:.1f}s] {text} (置信度: {confidence:.3f})")
            
            return {
                'success': True,
                'audio_info': audio_info,
                'transcribe_time': transcribe_time,
                'speed_ratio': speed_ratio,
                'text': text,
                'language': result.get('language'),
                'segments_count': len(segments),
                'avg_confidence': avg_confidence if segments else 0,
                'segments': segments
            }
            
        except Exception as e:
            print(f"❌ 转写失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'audio_info': audio_info
            }
    
    def _get_audio_info(self, audio_path: str) -> Dict:
        """获取音频文件信息"""
        try:
            # 使用librosa获取音频信息
            y, sr = librosa.load(audio_path, sr=None)
            duration = len(y) / sr
            
            # 使用soundfile获取更详细信息
            info = sf.info(audio_path)
            
            return {
                'duration': duration,
                'sample_rate': info.samplerate,
                'channels': info.channels,
                'format': info.format,
                'subtype': info.subtype,
                'frames': info.frames
            }
        except Exception as e:
            print(f"⚠️  获取音频信息失败: {e}")
            return {
                'duration': 0,
                'sample_rate': 0,
                'channels': 0,
                'format': 'unknown',
                'subtype': 'unknown',
                'frames': 0
            }
    
    def benchmark_models(self, audio_path: str, models: List[str] = None) -> Dict:
        """
        对比测试多个模型
        
        Args:
            audio_path: 测试音频文件路径
            models: 要测试的模型列表
            
        Returns:
            对比测试结果
        """
        if models is None:
            models = ["tiny", "base", "small"]
        
        print(f"\n🏁 开始模型对比测试")
        print(f"   测试音频: {audio_path}")
        print(f"   测试模型: {', '.join(models)}")
        
        results = {}
        
        for model_name in models:
            print(f"\n{'='*50}")
            print(f"测试模型: {model_name}")
            print(f"{'='*50}")
            
            # 重新加载模型
            self.model_size = model_name
            self.load_model()
            
            # 测试
            result = self.test_audio_file(audio_path)
            results[model_name] = result
        
        # 显示对比结果
        self._show_benchmark_results(results)
        
        return results
    
    def _show_benchmark_results(self, results: Dict) -> None:
        """显示对比测试结果"""
        print(f"\n📊 模型对比结果")
        print(f"{'='*80}")
        print(f"{'模型':<10} {'状态':<6} {'转写时间':<10} {'速度比':<8} {'片段数':<8} {'置信度':<10}")
        print(f"{'-'*80}")
        
        for model_name, result in results.items():
            if result['success']:
                status = "✅"
                transcribe_time = f"{result['transcribe_time']:.2f}s"
                speed_ratio = f"{result['speed_ratio']:.2f}x"
                segments_count = str(result['segments_count'])
                avg_confidence = f"{result['avg_confidence']:.3f}"
            else:
                status = "❌"
                transcribe_time = "N/A"
                speed_ratio = "N/A"
                segments_count = "N/A"
                avg_confidence = "N/A"
            
            print(f"{model_name:<10} {status:<6} {transcribe_time:<10} {speed_ratio:<8} {segments_count:<8} {avg_confidence:<10}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Whisper模型性能测试")
    parser.add_argument("--audio", "-a", required=True, help="测试音频文件路径")
    parser.add_argument("--model", "-m", default="base", help="模型大小 (tiny, base, small, medium, large, large-v3)")
    parser.add_argument("--benchmark", "-b", action="store_true", help="对比测试多个模型")
    parser.add_argument("--models", nargs="+", default=["tiny", "base", "small"], help="对比测试的模型列表")
    
    args = parser.parse_args()
    
    # 检查音频文件
    if not os.path.exists(args.audio):
        print(f"❌ 音频文件不存在: {args.audio}")
        sys.exit(1)
    
    # 创建测试器
    tester = WhisperTester(args.model)
    
    if args.benchmark:
        # 对比测试
        tester.benchmark_models(args.audio, args.models)
    else:
        # 单模型测试
        tester.load_model()
        result = tester.test_audio_file(args.audio)
        
        if result['success']:
            print(f"\n🎉 测试完成！")
        else:
            print(f"\n💥 测试失败: {result['error']}")
            sys.exit(1)

if __name__ == "__main__":
    main()
