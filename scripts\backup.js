#!/usr/bin/env node
/**
 * 数据备份脚本
 * 备份数据库、上传文件和配置
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const archiver = require('archiver');

// 配置
const config = {
  backupDir: './backups',
  uploadDir: './uploads',
  configFiles: ['.env', 'docker-compose.yml', 'nginx/nginx.conf'],
  dbConfig: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    database: process.env.DB_NAME || 'meeting_db',
    username: process.env.DB_USER || 'meeting_user',
    password: process.env.DB_PASSWORD || 'meeting_pass'
  },
  retention: {
    daily: 7,    // 保留7天的每日备份
    weekly: 4,   // 保留4周的每周备份
    monthly: 12  // 保留12个月的每月备份
  }
};

/**
 * 确保目录存在
 */
function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`📁 创建目录: ${dir}`);
  }
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
  const sizes = ['B', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 B';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

/**
 * 备份数据库
 */
async function backupDatabase(backupPath) {
  console.log('🗄️  开始备份数据库...');
  
  const sqlFile = path.join(backupPath, 'database.sql');
  const { host, port, database, username, password } = config.dbConfig;
  
  try {
    const command = `mysqldump -h ${host} -P ${port} -u ${username} -p${password} ${database}`;
    const output = execSync(command, { encoding: 'utf8' });
    
    fs.writeFileSync(sqlFile, output);
    
    const stats = fs.statSync(sqlFile);
    console.log(`✅ 数据库备份完成: ${formatFileSize(stats.size)}`);
    
    return sqlFile;
  } catch (error) {
    console.error('❌ 数据库备份失败:', error.message);
    throw error;
  }
}

/**
 * 备份上传文件
 */
async function backupUploads(backupPath) {
  console.log('📁 开始备份上传文件...');
  
  if (!fs.existsSync(config.uploadDir)) {
    console.log('⚠️  上传目录不存在，跳过文件备份');
    return null;
  }
  
  const uploadsBackup = path.join(backupPath, 'uploads.tar.gz');
  
  try {
    execSync(`tar -czf "${uploadsBackup}" -C "${path.dirname(config.uploadDir)}" "${path.basename(config.uploadDir)}"`);
    
    const stats = fs.statSync(uploadsBackup);
    console.log(`✅ 文件备份完成: ${formatFileSize(stats.size)}`);
    
    return uploadsBackup;
  } catch (error) {
    console.error('❌ 文件备份失败:', error.message);
    throw error;
  }
}

/**
 * 备份配置文件
 */
async function backupConfigs(backupPath) {
  console.log('⚙️  开始备份配置文件...');
  
  const configDir = path.join(backupPath, 'configs');
  ensureDir(configDir);
  
  let totalSize = 0;
  
  for (const configFile of config.configFiles) {
    if (fs.existsSync(configFile)) {
      const destFile = path.join(configDir, path.basename(configFile));
      fs.copyFileSync(configFile, destFile);
      
      const stats = fs.statSync(destFile);
      totalSize += stats.size;
      
      console.log(`  📄 ${configFile} -> ${destFile}`);
    } else {
      console.log(`  ⚠️  配置文件不存在: ${configFile}`);
    }
  }
  
  console.log(`✅ 配置备份完成: ${formatFileSize(totalSize)}`);
  return configDir;
}

/**
 * 创建备份归档
 */
async function createArchive(backupPath, archiveName) {
  console.log('📦 创建备份归档...');
  
  const archivePath = path.join(config.backupDir, archiveName);
  
  return new Promise((resolve, reject) => {
    const output = fs.createWriteStream(archivePath);
    const archive = archiver('zip', { zlib: { level: 9 } });
    
    output.on('close', () => {
      const stats = fs.statSync(archivePath);
      console.log(`✅ 归档创建完成: ${archiveName} (${formatFileSize(stats.size)})`);
      resolve(archivePath);
    });
    
    archive.on('error', (err) => {
      console.error('❌ 归档创建失败:', err.message);
      reject(err);
    });
    
    archive.pipe(output);
    archive.directory(backupPath, false);
    archive.finalize();
  });
}

/**
 * 清理过期备份
 */
function cleanupOldBackups() {
  console.log('🧹 清理过期备份...');
  
  if (!fs.existsSync(config.backupDir)) {
    return;
  }
  
  const files = fs.readdirSync(config.backupDir)
    .filter(file => file.endsWith('.zip'))
    .map(file => {
      const filePath = path.join(config.backupDir, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        path: filePath,
        mtime: stats.mtime,
        size: stats.size
      };
    })
    .sort((a, b) => b.mtime - a.mtime);
  
  const now = new Date();
  let deletedCount = 0;
  let deletedSize = 0;
  
  files.forEach(file => {
    const age = now - file.mtime;
    const days = age / (1000 * 60 * 60 * 24);
    
    let shouldDelete = false;
    
    // 根据备份类型和保留策略决定是否删除
    if (file.name.includes('daily') && days > config.retention.daily) {
      shouldDelete = true;
    } else if (file.name.includes('weekly') && days > config.retention.weekly * 7) {
      shouldDelete = true;
    } else if (file.name.includes('monthly') && days > config.retention.monthly * 30) {
      shouldDelete = true;
    }
    
    if (shouldDelete) {
      fs.unlinkSync(file.path);
      deletedCount++;
      deletedSize += file.size;
      console.log(`  🗑️  删除过期备份: ${file.name}`);
    }
  });
  
  if (deletedCount > 0) {
    console.log(`✅ 清理完成: 删除 ${deletedCount} 个文件，释放 ${formatFileSize(deletedSize)}`);
  } else {
    console.log('✅ 无需清理过期备份');
  }
}

/**
 * 生成备份报告
 */
function generateReport(backupInfo) {
  const report = {
    timestamp: new Date().toISOString(),
    backup: backupInfo,
    system: {
      platform: process.platform,
      nodeVersion: process.version,
      memory: process.memoryUsage(),
      uptime: process.uptime()
    }
  };
  
  const reportFile = path.join(config.backupDir, 'backup-report.json');
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  
  console.log(`📊 备份报告已生成: ${reportFile}`);
  return reportFile;
}

/**
 * 主备份函数
 */
async function performBackup(type = 'manual') {
  const startTime = Date.now();
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupName = `backup-${type}-${timestamp}`;
  const tempBackupPath = path.join(config.backupDir, 'temp', backupName);
  
  console.log('🚀 开始系统备份...');
  console.log(`📅 时间: ${new Date().toLocaleString()}`);
  console.log(`📝 类型: ${type}`);
  console.log(`📁 备份名称: ${backupName}`);
  console.log('');
  
  try {
    // 确保备份目录存在
    ensureDir(config.backupDir);
    ensureDir(tempBackupPath);
    
    const backupInfo = {
      name: backupName,
      type: type,
      timestamp: new Date().toISOString(),
      components: {}
    };
    
    // 备份数据库
    try {
      const dbBackup = await backupDatabase(tempBackupPath);
      backupInfo.components.database = {
        status: 'success',
        file: path.basename(dbBackup),
        size: fs.statSync(dbBackup).size
      };
    } catch (error) {
      backupInfo.components.database = {
        status: 'failed',
        error: error.message
      };
    }
    
    // 备份上传文件
    try {
      const uploadsBackup = await backupUploads(tempBackupPath);
      if (uploadsBackup) {
        backupInfo.components.uploads = {
          status: 'success',
          file: path.basename(uploadsBackup),
          size: fs.statSync(uploadsBackup).size
        };
      } else {
        backupInfo.components.uploads = {
          status: 'skipped',
          reason: 'No uploads directory'
        };
      }
    } catch (error) {
      backupInfo.components.uploads = {
        status: 'failed',
        error: error.message
      };
    }
    
    // 备份配置文件
    try {
      await backupConfigs(tempBackupPath);
      backupInfo.components.configs = {
        status: 'success'
      };
    } catch (error) {
      backupInfo.components.configs = {
        status: 'failed',
        error: error.message
      };
    }
    
    // 创建归档
    const archiveName = `${backupName}.zip`;
    const archivePath = await createArchive(tempBackupPath, archiveName);
    
    backupInfo.archive = {
      name: archiveName,
      path: archivePath,
      size: fs.statSync(archivePath).size
    };
    
    // 清理临时文件
    fs.rmSync(tempBackupPath, { recursive: true, force: true });
    
    // 清理过期备份
    cleanupOldBackups();
    
    // 生成报告
    generateReport(backupInfo);
    
    const duration = Date.now() - startTime;
    console.log('');
    console.log('🎉 备份完成！');
    console.log(`⏱️  耗时: ${Math.round(duration / 1000)}秒`);
    console.log(`📦 归档: ${archiveName}`);
    console.log(`💾 大小: ${formatFileSize(backupInfo.archive.size)}`);
    
    return backupInfo;
    
  } catch (error) {
    console.error('💥 备份失败:', error.message);
    
    // 清理临时文件
    if (fs.existsSync(tempBackupPath)) {
      fs.rmSync(tempBackupPath, { recursive: true, force: true });
    }
    
    throw error;
  }
}

/**
 * 命令行处理
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
使用方法: node backup.js [选项]

选项:
  -h, --help     显示帮助信息
  --type TYPE    备份类型 (manual, daily, weekly, monthly)
  --list         列出现有备份
  --cleanup      仅执行清理操作

示例:
  node backup.js                    # 手动备份
  node backup.js --type daily       # 每日备份
  node backup.js --list             # 列出备份
  node backup.js --cleanup          # 清理过期备份
`);
    return;
  }
  
  if (args.includes('--list')) {
    // 列出现有备份
    if (!fs.existsSync(config.backupDir)) {
      console.log('📁 备份目录不存在');
      return;
    }
    
    const backups = fs.readdirSync(config.backupDir)
      .filter(file => file.endsWith('.zip'))
      .map(file => {
        const filePath = path.join(config.backupDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          size: formatFileSize(stats.size),
          date: stats.mtime.toLocaleString()
        };
      })
      .sort((a, b) => new Date(b.date) - new Date(a.date));
    
    console.log('📋 现有备份列表:');
    console.log('');
    backups.forEach(backup => {
      console.log(`📦 ${backup.name}`);
      console.log(`   💾 大小: ${backup.size}`);
      console.log(`   📅 日期: ${backup.date}`);
      console.log('');
    });
    
    return;
  }
  
  if (args.includes('--cleanup')) {
    cleanupOldBackups();
    return;
  }
  
  // 执行备份
  const typeIndex = args.indexOf('--type');
  const type = typeIndex !== -1 ? args[typeIndex + 1] : 'manual';
  
  await performBackup(type);
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('💥 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('💥 备份脚本执行失败:', error.message);
    process.exit(1);
  });
}
