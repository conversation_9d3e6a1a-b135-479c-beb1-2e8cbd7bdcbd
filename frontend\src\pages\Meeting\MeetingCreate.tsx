import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  DatePicker,
  Select,
  Button,
  Space,
  Row,
  Col,
  Steps,
  message,
  Upload,
  Typography,
  Divider,
  Tag,
} from 'antd';
import {
  SaveOutlined,
  ArrowLeftOutlined,
  SoundOutlined,
  UploadOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { useMeetingStore } from '@/stores/meetingStore';
import { CreateMeetingRequest } from '@/types/api';
import AudioRecorder from '@/components/AudioRecorder/AudioRecorder';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

const MeetingCreate: React.FC = () => {
  const navigate = useNavigate();
  const { createMeeting, uploadAudio, isLoading } = useMeetingStore();
  const [form] = Form.useForm();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [meetingId, setMeetingId] = useState<number | null>(null);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  // 步骤配置
  const steps = [
    {
      title: '基本信息',
      icon: <FileTextOutlined />,
      description: '填写会议基本信息',
    },
    {
      title: '录音',
      icon: <SoundOutlined />,
      description: '开始会议录音',
    },
    {
      title: '完成',
      icon: <CalendarOutlined />,
      description: '会议创建完成',
    },
  ];

  // 处理表单提交
  const handleFormSubmit = async (values: any) => {
    try {
      const meetingData: CreateMeetingRequest = {
        title: values.title,
        date: values.date.toISOString(),
        customerName: values.customerName,
        participants: values.participants || [],
        tags: values.tags || [],
      };

      const meeting = await createMeeting(meetingData);
      setMeetingId(meeting.id);
      setCurrentStep(1);
      message.success('会议创建成功，可以开始录音了');
    } catch (error) {
      message.error('创建会议失败');
    }
  };

  // 处理录音完成
  const handleRecordingComplete = (blob: Blob) => {
    setAudioBlob(blob);
    message.success('录音完成，正在上传...');
    handleAudioUpload(blob);
  };

  // 处理音频上传
  const handleAudioUpload = async (blob: Blob) => {
    if (!meetingId) {
      message.error('会议ID不存在');
      return;
    }

    try {
      // 创建文件对象
      const audioFile = new File([blob], `meeting-${meetingId}-${Date.now()}.wav`, {
        type: 'audio/wav',
      });

      await uploadAudio(meetingId, audioFile, (progress) => {
        setUploadProgress(progress);
      });

      setCurrentStep(2);
      message.success('音频上传成功，系统正在进行转写处理');
    } catch (error) {
      message.error('音频上传失败');
    }
  };

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    if (!meetingId) {
      message.error('请先创建会议');
      return false;
    }

    try {
      await uploadAudio(meetingId, file, (progress) => {
        setUploadProgress(progress);
      });

      setCurrentStep(2);
      message.success('音频上传成功');
    } catch (error) {
      message.error('音频上传失败');
    }

    return false; // 阻止默认上传行为
  };

  // 跳过录音
  const handleSkipRecording = () => {
    setCurrentStep(2);
    message.info('已跳过录音，您可以稍后上传音频文件');
  };

  // 完成创建
  const handleComplete = () => {
    if (meetingId) {
      navigate(`/meetings/${meetingId}`);
    } else {
      navigate('/meetings');
    }
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Card title="会议基本信息" style={{ marginTop: 24 }}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleFormSubmit}
              initialValues={{
                date: dayjs(),
                participants: [],
                tags: [],
              }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="title"
                    label="会议标题"
                    rules={[{ required: true, message: '请输入会议标题' }]}
                  >
                    <Input placeholder="请输入会议标题" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="date"
                    label="会议时间"
                    rules={[{ required: true, message: '请选择会议时间' }]}
                  >
                    <DatePicker
                      showTime
                      style={{ width: '100%' }}
                      placeholder="选择会议时间"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="customerName" label="客户名称">
                    <Input placeholder="请输入客户名称（可选）" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="participants" label="参与人员">
                    <Select
                      mode="tags"
                      placeholder="输入参与人员姓名，按回车添加"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item name="tags" label="会议标签">
                <Select
                  mode="tags"
                  placeholder="添加会议标签，如：产品讨论、技术评审等"
                  style={{ width: '100%' }}
                />
              </Form.Item>

              <Form.Item name="description" label="会议描述">
                <TextArea
                  rows={4}
                  placeholder="请输入会议描述或议程（可选）"
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={isLoading}
                    icon={<SaveOutlined />}
                  >
                    创建会议
                  </Button>
                  <Button onClick={() => navigate('/meetings')}>
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        );

      case 1:
        return (
          <div style={{ marginTop: 24 }}>
            <Card title="会议录音" style={{ marginBottom: 24 }}>
              <div style={{ textAlign: 'center', marginBottom: 24 }}>
                <Title level={4}>开始录制会议内容</Title>
                <Text type="secondary">
                  您可以选择实时录音或上传已有的音频文件
                </Text>
              </div>

              <AudioRecorder
                onRecordingComplete={handleRecordingComplete}
                maxDuration={14400} // 4小时
              />

              <Divider>或</Divider>

              <div style={{ textAlign: 'center' }}>
                <Upload
                  accept="audio/*"
                  beforeUpload={handleFileUpload}
                  showUploadList={false}
                >
                  <Button icon={<UploadOutlined />} size="large">
                    上传音频文件
                  </Button>
                </Upload>
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">
                    支持 WAV、MP3、M4A、AAC、FLAC 等格式，最大 500MB
                  </Text>
                </div>
              </div>

              {uploadProgress > 0 && uploadProgress < 100 && (
                <div style={{ marginTop: 24 }}>
                  <Text>上传进度: {uploadProgress}%</Text>
                </div>
              )}

              <div style={{ marginTop: 24, textAlign: 'center' }}>
                <Space>
                  <Button onClick={() => setCurrentStep(0)}>
                    上一步
                  </Button>
                  <Button type="dashed" onClick={handleSkipRecording}>
                    跳过录音
                  </Button>
                </Space>
              </div>
            </Card>
          </div>
        );

      case 2:
        return (
          <Card title="创建完成" style={{ marginTop: 24 }}>
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <div style={{ fontSize: 64, color: '#52c41a', marginBottom: 16 }}>
                ✅
              </div>
              <Title level={3}>会议创建成功！</Title>
              <Text type="secondary" style={{ fontSize: 16 }}>
                {audioBlob ? '音频已上传，系统正在进行转写处理' : '会议已创建，您可以稍后上传音频'}
              </Text>

              <div style={{ marginTop: 32 }}>
                <Space size="large">
                  <div>
                    <Text strong>会议ID</Text>
                    <br />
                    <Tag color="blue">{meetingId}</Tag>
                  </div>
                  <div>
                    <Text strong>状态</Text>
                    <br />
                    <Tag color={audioBlob ? 'processing' : 'warning'}>
                      {audioBlob ? '转写中' : '待上传'}
                    </Tag>
                  </div>
                </Space>
              </div>

              <div style={{ marginTop: 32 }}>
                <Space>
                  <Button
                    type="primary"
                    size="large"
                    onClick={handleComplete}
                  >
                    查看会议详情
                  </Button>
                  <Button
                    size="large"
                    onClick={() => navigate('/meetings')}
                  >
                    返回会议列表
                  </Button>
                </Space>
              </div>
            </div>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/meetings')}
          >
            返回
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            创建会议
          </Title>
        </Space>
      </div>

      {/* 步骤指示器 */}
      <Card style={{ marginBottom: 24 }}>
        <Steps
          current={currentStep}
          items={steps}
          size="small"
        />
      </Card>

      {/* 步骤内容 */}
      {renderStepContent()}
    </div>
  );
};

export default MeetingCreate;
