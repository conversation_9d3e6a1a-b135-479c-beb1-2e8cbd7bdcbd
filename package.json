{"name": "smart-meeting-system", "version": "1.0.0", "description": "智能会议系统 - 支持PC录音、语音转写、智能纪要生成", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\" \"npm run dev:ai\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev:ai": "cd ai-service && python main.py", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend && npm run test:ai", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "test:ai": "cd ai-service && python -m pytest", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "setup": "npm run setup:frontend && npm run setup:backend && npm run setup:ai", "setup:frontend": "cd frontend && npm install", "setup:backend": "cd backend && npm install", "setup:ai": "cd ai-service && pip install -r requirements.txt", "db:migrate": "cd backend && npx prisma migrate dev", "db:seed": "cd backend && npx prisma db seed", "db:studio": "cd backend && npx prisma studio"}, "keywords": ["meeting", "transcription", "AI", "speech-to-text", "whisper", "recording"], "author": "Smart Meeting Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "backend"]}