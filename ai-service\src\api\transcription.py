"""
语音转写API路由
"""

import asyncio
import uuid
from pathlib import Path
from typing import List, Optional
from fastapi import APIRouter, UploadFile, File, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field
from loguru import logger

from ..config.app_config import settings

router = APIRouter()

# 请求模型
class TranscriptionRequest(BaseModel):
    language: str = Field(default="zh", description="语言代码")
    hotwords: Optional[List[str]] = Field(default=None, description="热词列表")
    enable_timestamps: bool = Field(default=True, description="是否启用时间戳")

class TranscriptionResponse(BaseModel):
    task_id: str = Field(description="任务ID")
    status: str = Field(description="任务状态")
    message: str = Field(description="状态消息")

class TranscriptionResult(BaseModel):
    task_id: str = Field(description="任务ID")
    status: str = Field(description="任务状态")
    text: Optional[str] = Field(default=None, description="转写文本")
    language: Optional[str] = Field(default=None, description="检测到的语言")
    segments: Optional[List[dict]] = Field(default=None, description="文本片段")
    words: Optional[List[dict]] = Field(default=None, description="单词级时间戳")
    duration: Optional[float] = Field(default=None, description="音频时长")
    processing_time: Optional[float] = Field(default=None, description="处理时间")
    error: Optional[str] = Field(default=None, description="错误信息")

# 任务状态存储（生产环境应使用Redis）
task_storage = {}

def get_transcription_service():
    """获取转写服务实例"""
    from ..main import app
    return app.state.get_transcription_service()

@router.post("/upload", response_model=TranscriptionResponse)
async def upload_audio_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    language: str = "zh",
    hotwords: Optional[str] = None,
    enable_timestamps: bool = True,
    transcription_service = Depends(get_transcription_service)
):
    """
    上传音频文件进行转写
    """
    try:
        # 验证文件类型
        if not file.content_type or not any(
            file.content_type.startswith(fmt) for fmt in ["audio/", "video/"]
        ):
            raise HTTPException(
                status_code=400,
                detail="不支持的文件类型，请上传音频或视频文件"
            )
        
        # 验证文件大小
        if file.size and file.size > 500 * 1024 * 1024:  # 500MB
            raise HTTPException(
                status_code=400,
                detail="文件大小超过限制（500MB）"
            )
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 保存上传的文件
        file_extension = Path(file.filename or "audio.wav").suffix
        if not file_extension:
            file_extension = ".wav"
        
        upload_path = settings.AUDIO_TEMP_DIR / f"{task_id}{file_extension}"
        
        with open(upload_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 解析热词
        hotwords_list = None
        if hotwords:
            hotwords_list = [word.strip() for word in hotwords.split(",") if word.strip()]
        
        # 初始化任务状态
        task_storage[task_id] = {
            "status": "pending",
            "message": "任务已创建，等待处理",
            "created_at": asyncio.get_event_loop().time()
        }
        
        # 添加后台任务
        background_tasks.add_task(
            process_transcription_task,
            task_id,
            upload_path,
            language,
            hotwords_list,
            enable_timestamps,
            transcription_service
        )
        
        logger.info(f"创建转写任务: {task_id}, 文件: {file.filename}")
        
        return TranscriptionResponse(
            task_id=task_id,
            status="pending",
            message="任务已创建，正在处理中"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@router.get("/status/{task_id}", response_model=TranscriptionResult)
async def get_transcription_status(task_id: str):
    """
    获取转写任务状态
    """
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task_data = task_storage[task_id]
    
    return TranscriptionResult(
        task_id=task_id,
        status=task_data["status"],
        text=task_data.get("text"),
        language=task_data.get("language"),
        segments=task_data.get("segments"),
        words=task_data.get("words"),
        duration=task_data.get("duration"),
        processing_time=task_data.get("processing_time"),
        error=task_data.get("error")
    )

@router.delete("/task/{task_id}")
async def delete_transcription_task(task_id: str):
    """
    删除转写任务
    """
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    # 删除任务数据
    task_data = task_storage.pop(task_id)
    
    # 删除临时文件
    if "file_path" in task_data:
        file_path = Path(task_data["file_path"])
        if file_path.exists():
            file_path.unlink()
    
    logger.info(f"删除转写任务: {task_id}")
    
    return {"message": "任务已删除"}

@router.get("/tasks")
async def list_transcription_tasks():
    """
    获取所有转写任务列表
    """
    tasks = []
    current_time = asyncio.get_event_loop().time()
    
    for task_id, task_data in task_storage.items():
        tasks.append({
            "task_id": task_id,
            "status": task_data["status"],
            "created_at": task_data["created_at"],
            "age": current_time - task_data["created_at"]
        })
    
    return {"tasks": tasks}

@router.post("/stream", response_model=TranscriptionResponse)
async def transcribe_audio_stream(
    background_tasks: BackgroundTasks,
    request: TranscriptionRequest,
    audio_data: bytes,
    transcription_service = Depends(get_transcription_service)
):
    """
    流式音频转写
    """
    try:
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 初始化任务状态
        task_storage[task_id] = {
            "status": "pending",
            "message": "流式转写任务已创建",
            "created_at": asyncio.get_event_loop().time()
        }
        
        # 添加后台任务
        background_tasks.add_task(
            process_stream_transcription_task,
            task_id,
            [audio_data],
            request.language,
            request.hotwords,
            transcription_service
        )
        
        logger.info(f"创建流式转写任务: {task_id}")
        
        return TranscriptionResponse(
            task_id=task_id,
            status="pending",
            message="流式转写任务已创建"
        )
        
    except Exception as e:
        logger.error(f"流式转写失败: {e}")
        raise HTTPException(status_code=500, detail=f"流式转写失败: {str(e)}")

async def process_transcription_task(
    task_id: str,
    file_path: Path,
    language: str,
    hotwords: Optional[List[str]],
    enable_timestamps: bool,
    transcription_service
):
    """
    处理转写任务
    """
    start_time = asyncio.get_event_loop().time()
    
    try:
        # 更新任务状态
        task_storage[task_id].update({
            "status": "processing",
            "message": "正在转写音频",
            "file_path": str(file_path)
        })
        
        # 估算音频时长
        duration = await transcription_service.estimate_duration(file_path)
        task_storage[task_id]["duration"] = duration
        
        # 执行转写
        result = await transcription_service.transcribe_file(
            audio_path=file_path,
            language=language,
            hotwords=hotwords,
            enable_timestamps=enable_timestamps
        )
        
        # 计算处理时间
        processing_time = asyncio.get_event_loop().time() - start_time
        
        # 更新任务状态
        task_storage[task_id].update({
            "status": "completed",
            "message": "转写完成",
            "text": result["text"],
            "language": result["language"],
            "segments": result["segments"],
            "words": result["words"],
            "processing_time": processing_time
        })
        
        logger.info(f"转写任务完成: {task_id}, 耗时: {processing_time:.2f}秒")
        
    except Exception as e:
        logger.error(f"转写任务失败: {task_id}, 错误: {e}")
        
        task_storage[task_id].update({
            "status": "failed",
            "message": "转写失败",
            "error": str(e),
            "processing_time": asyncio.get_event_loop().time() - start_time
        })
    
    finally:
        # 清理临时文件
        if file_path.exists():
            file_path.unlink()

async def process_stream_transcription_task(
    task_id: str,
    audio_chunks: List[bytes],
    language: str,
    hotwords: Optional[List[str]],
    transcription_service
):
    """
    处理流式转写任务
    """
    start_time = asyncio.get_event_loop().time()
    
    try:
        # 更新任务状态
        task_storage[task_id].update({
            "status": "processing",
            "message": "正在处理流式音频"
        })
        
        # 执行流式转写
        result = await transcription_service.transcribe_stream(
            audio_chunks=audio_chunks,
            language=language,
            hotwords=hotwords
        )
        
        # 计算处理时间
        processing_time = asyncio.get_event_loop().time() - start_time
        
        # 更新任务状态
        task_storage[task_id].update({
            "status": "completed",
            "message": "流式转写完成",
            "text": result["text"],
            "language": result["language"],
            "segments": result["segments"],
            "words": result["words"],
            "processing_time": processing_time
        })
        
        logger.info(f"流式转写任务完成: {task_id}, 耗时: {processing_time:.2f}秒")
        
    except Exception as e:
        logger.error(f"流式转写任务失败: {task_id}, 错误: {e}")
        
        task_storage[task_id].update({
            "status": "failed",
            "message": "流式转写失败",
            "error": str(e),
            "processing_time": asyncio.get_event_loop().time() - start_time
        })
