import React, { useState, useCallback } from 'react';
import { <PERSON>ton, Card, Progress, Alert, Space, Typography, Row, Col, Statistic } from 'antd';
import { 
  AudioOutlined, 
  PauseOutlined, 
  PlayCircleOutlined, 
  StopOutlined,
  SoundOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { useRecorder } from '@/hooks/useRecorder';
import { RecordingStatus } from '@/types/api';

const { Title, Text } = Typography;

interface AudioRecorderProps {
  onRecordingComplete?: (audioBlob: Blob) => void;
  onRecordingStart?: () => void;
  onRecordingStop?: () => void;
  disabled?: boolean;
  maxDuration?: number; // 最大录音时长(秒)
}

const AudioRecorder: React.FC<AudioRecorderProps> = ({
  onRecordingComplete,
  onRecordingStart,
  onRecordingStop,
  disabled = false,
  maxDuration = 14400, // 4小时
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const {
    status,
    startRecording,
    pauseRecording,
    resumeRecording,
    stopRecording,
    isSupported,
    error,
  } = useRecorder({
    onRecordingStart: () => {
      onRecordingStart?.();
    },
    onRecordingStop: (audioBlob) => {
      onRecordingComplete?.(audioBlob);
      onRecordingStop?.();
    },
    config: {
      sampleRate: 44100,
      channels: 1,
      bitDepth: 16,
      format: 'audio/wav',
    },
  });

  // 格式化时间显示
  const formatTime = useCallback((seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // 获取质量指示器
  const getQualityIndicator = useCallback(() => {
    switch (status.quality) {
      case 'good':
        return {
          icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
          text: '音质良好',
          color: '#52c41a',
        };
      case 'fair':
        return {
          icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
          text: '音质一般',
          color: '#faad14',
        };
      case 'poor':
        return {
          icon: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
          text: '音质较差',
          color: '#ff4d4f',
        };
      default:
        return {
          icon: <SoundOutlined />,
          text: '检测中',
          color: '#d9d9d9',
        };
    }
  }, [status.quality]);

  // 获取录音状态文本
  const getStatusText = useCallback(() => {
    if (!status.isRecording) {
      return '准备录音';
    }
    if (status.isPaused) {
      return '录音已暂停';
    }
    return '正在录音';
  }, [status.isRecording, status.isPaused]);

  // 获取录音状态颜色
  const getStatusColor = useCallback(() => {
    if (!status.isRecording) {
      return '#1890ff';
    }
    if (status.isPaused) {
      return '#faad14';
    }
    return '#ff4d4f';
  }, [status.isRecording, status.isPaused]);

  // 处理开始/继续录音
  const handleStartOrResume = useCallback(async () => {
    if (status.isPaused) {
      resumeRecording();
    } else {
      await startRecording();
    }
  }, [status.isPaused, resumeRecording, startRecording]);

  // 处理停止录音
  const handleStop = useCallback(async () => {
    await stopRecording();
  }, [stopRecording]);

  // 计算进度百分比
  const progressPercent = maxDuration > 0 ? Math.min((status.duration / maxDuration) * 100, 100) : 0;

  const qualityIndicator = getQualityIndicator();

  if (!isSupported) {
    return (
      <Card>
        <Alert
          message="浏览器不支持录音功能"
          description="请使用Chrome、Firefox或Edge等现代浏览器"
          type="error"
          showIcon
        />
      </Card>
    );
  }

  return (
    <Card 
      title={
        <Space>
          <AudioOutlined />
          <span>会议录音</span>
        </Space>
      }
      extra={
        <Button 
          type="link" 
          size="small"
          onClick={() => setShowAdvanced(!showAdvanced)}
        >
          {showAdvanced ? '简单模式' : '高级模式'}
        </Button>
      }
      className={`audio-recorder ${status.isRecording ? 'recording' : ''} ${status.isPaused ? 'paused' : ''}`}
    >
      {error && (
        <Alert
          message="录音错误"
          description={error}
          type="error"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 主要状态显示 */}
      <div style={{ textAlign: 'center', marginBottom: 24 }}>
        <Title level={3} style={{ color: getStatusColor(), marginBottom: 8 }}>
          {getStatusText()}
        </Title>
        
        <Title level={1} style={{ fontFamily: 'monospace', marginBottom: 16 }}>
          {formatTime(status.duration / 1000)}
        </Title>

        {/* 音量指示器 */}
        <div style={{ marginBottom: 16 }}>
          <Text type="secondary">音量</Text>
          <Progress
            percent={status.volume}
            showInfo={false}
            strokeColor={{
              '0%': '#52c41a',
              '50%': '#faad14',
              '100%': '#ff4d4f',
            }}
            style={{ marginTop: 8 }}
          />
        </div>

        {/* 质量指示器 */}
        <Space>
          {qualityIndicator.icon}
          <Text style={{ color: qualityIndicator.color }}>
            {qualityIndicator.text}
          </Text>
        </Space>
      </div>

      {/* 控制按钮 */}
      <Row gutter={16} justify="center" style={{ marginBottom: 16 }}>
        <Col>
          <Button
            type="primary"
            size="large"
            icon={status.isPaused ? <PlayCircleOutlined /> : <AudioOutlined />}
            onClick={handleStartOrResume}
            disabled={disabled || (status.isRecording && !status.isPaused)}
            loading={status.isRecording && !status.isPaused}
          >
            {status.isPaused ? '继续录音' : '开始录音'}
          </Button>
        </Col>
        
        <Col>
          <Button
            size="large"
            icon={<PauseOutlined />}
            onClick={pauseRecording}
            disabled={disabled || !status.isRecording || status.isPaused}
          >
            暂停
          </Button>
        </Col>
        
        <Col>
          <Button
            danger
            size="large"
            icon={<StopOutlined />}
            onClick={handleStop}
            disabled={disabled || !status.isRecording}
          >
            停止录音
          </Button>
        </Col>
      </Row>

      {/* 高级信息 */}
      {showAdvanced && (
        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="录音时长"
              value={status.duration / 1000}
              precision={1}
              suffix="秒"
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="音量水平"
              value={status.volume}
              precision={1}
              suffix="%"
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="噪音水平"
              value={status.noiseLevel}
              precision={1}
              suffix="%"
            />
          </Col>
        </Row>
      )}

      {/* 进度条 */}
      {maxDuration > 0 && status.duration > 0 && (
        <div style={{ marginTop: 16 }}>
          <Text type="secondary">录音进度</Text>
          <Progress
            percent={progressPercent}
            format={() => `${formatTime(status.duration / 1000)} / ${formatTime(maxDuration)}`}
            status={progressPercent >= 100 ? 'exception' : 'active'}
          />
        </div>
      )}

      {/* 快捷键提示 */}
      <div style={{ marginTop: 16, textAlign: 'center' }}>
        <Text type="secondary" style={{ fontSize: 12 }}>
          💡 提示：按空格键快速开始/暂停录音
        </Text>
      </div>
    </Card>
  );
};

export default AudioRecorder;
