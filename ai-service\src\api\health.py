"""
健康检查API路由
"""

import psutil
import torch
from fastapi import APIRouter, Depends
from pydantic import BaseModel
from typing import Dict, Optional
import asyncio
from loguru import logger

from ..config.app_config import settings

router = APIRouter()

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    version: str
    uptime: float
    system: Dict
    services: Dict

class SystemInfo(BaseModel):
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    gpu_available: bool
    gpu_memory_used: Optional[float] = None
    gpu_memory_total: Optional[float] = None

def get_transcription_service():
    """获取转写服务实例"""
    try:
        from ..main import app
        return app.state.get_transcription_service()
    except:
        return None

@router.get("/", response_model=HealthResponse)
async def health_check():
    """基础健康检查"""
    import time
    from datetime import datetime
    
    # 系统信息
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    # GPU信息
    gpu_available = torch.cuda.is_available()
    gpu_memory_used = None
    gpu_memory_total = None
    
    if gpu_available:
        try:
            gpu_memory_used = torch.cuda.memory_allocated(0) / 1024**3  # GB
            gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
        except:
            pass
    
    # 服务状态
    services_status = {
        "transcription": "unknown",
        "speaker_diarization": "unknown",
        "text_enhancement": "unknown"
    }
    
    # 检查转写服务
    try:
        transcription_service = get_transcription_service()
        if transcription_service and transcription_service.is_initialized:
            services_status["transcription"] = "healthy"
        else:
            services_status["transcription"] = "initializing"
    except Exception as e:
        services_status["transcription"] = f"error: {str(e)}"
    
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        version="1.0.0",
        uptime=time.time(),
        system={
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_available_gb": memory.available / 1024**3,
            "disk_percent": disk.percent,
            "disk_free_gb": disk.free / 1024**3,
            "gpu_available": gpu_available,
            "gpu_memory_used_gb": gpu_memory_used,
            "gpu_memory_total_gb": gpu_memory_total,
        },
        services=services_status
    )

@router.get("/detailed")
async def detailed_health_check():
    """详细健康检查"""
    try:
        # 基础健康检查
        basic_health = await health_check()
        
        # 额外的详细信息
        detailed_info = {
            "config": {
                "whisper_model_size": settings.WHISPER_MODEL_SIZE,
                "audio_sample_rate": settings.AUDIO_SAMPLE_RATE,
                "max_concurrent_tasks": settings.MAX_CONCURRENT_TASKS,
                "model_cache_dir": str(settings.MODEL_CACHE_DIR),
            },
            "system_details": {
                "python_version": f"{psutil.PYTHON_VERSION}",
                "cpu_count": psutil.cpu_count(),
                "boot_time": psutil.boot_time(),
                "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
            },
            "disk_usage": {
                "model_cache": await _get_directory_size(settings.MODEL_CACHE_DIR),
                "temp_dir": await _get_directory_size(settings.AUDIO_TEMP_DIR),
            }
        }
        
        # 合并信息
        result = basic_health.dict()
        result.update(detailed_info)
        
        return result
        
    except Exception as e:
        logger.error(f"详细健康检查失败: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@router.get("/models")
async def models_status():
    """模型状态检查"""
    try:
        transcription_service = get_transcription_service()
        
        if not transcription_service:
            return {
                "status": "error",
                "message": "转写服务未初始化"
            }
        
        model_info = transcription_service.get_model_info()
        
        return {
            "status": "ok",
            "models": {
                "whisper": {
                    "model_size": model_info["model_size"],
                    "device": model_info["device"],
                    "is_initialized": model_info["is_initialized"],
                    "gpu_available": model_info["gpu_available"],
                    "gpu_memory_gb": model_info["gpu_memory"] / 1024**3 if model_info["gpu_memory"] > 0 else 0,
                }
            }
        }
        
    except Exception as e:
        logger.error(f"模型状态检查失败: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@router.get("/performance")
async def performance_metrics():
    """性能指标"""
    try:
        # CPU信息
        cpu_times = psutil.cpu_times()
        cpu_stats = {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "cpu_count_logical": psutil.cpu_count(logical=True),
            "cpu_count_physical": psutil.cpu_count(logical=False),
            "cpu_freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
            "cpu_times": cpu_times._asdict(),
        }
        
        # 内存信息
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        memory_stats = {
            "virtual_memory": memory._asdict(),
            "swap_memory": swap._asdict(),
        }
        
        # 网络信息
        network = psutil.net_io_counters()
        network_stats = network._asdict() if network else {}
        
        # GPU信息
        gpu_stats = {}
        if torch.cuda.is_available():
            try:
                gpu_stats = {
                    "device_count": torch.cuda.device_count(),
                    "current_device": torch.cuda.current_device(),
                    "device_name": torch.cuda.get_device_name(0),
                    "memory_allocated": torch.cuda.memory_allocated(0),
                    "memory_reserved": torch.cuda.memory_reserved(0),
                    "max_memory_allocated": torch.cuda.max_memory_allocated(0),
                }
            except Exception as e:
                gpu_stats = {"error": str(e)}
        
        return {
            "status": "ok",
            "timestamp": asyncio.get_event_loop().time(),
            "cpu": cpu_stats,
            "memory": memory_stats,
            "network": network_stats,
            "gpu": gpu_stats,
        }
        
    except Exception as e:
        logger.error(f"性能指标获取失败: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@router.post("/test")
async def test_services():
    """测试服务功能"""
    try:
        results = {}
        
        # 测试转写服务
        try:
            transcription_service = get_transcription_service()
            if transcription_service:
                # 获取支持的语言
                languages = await transcription_service.get_supported_languages()
                results["transcription"] = {
                    "status": "ok",
                    "supported_languages": languages,
                    "is_initialized": transcription_service.is_initialized
                }
            else:
                results["transcription"] = {
                    "status": "error",
                    "message": "服务未初始化"
                }
        except Exception as e:
            results["transcription"] = {
                "status": "error",
                "error": str(e)
            }
        
        return {
            "status": "ok",
            "test_results": results
        }
        
    except Exception as e:
        logger.error(f"服务测试失败: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

async def _get_directory_size(directory_path) -> Dict:
    """获取目录大小"""
    try:
        total_size = 0
        file_count = 0
        
        for dirpath, dirnames, filenames in directory_path.rglob('*'):
            for filename in filenames:
                file_path = dirpath / filename
                if file_path.exists():
                    total_size += file_path.stat().st_size
                    file_count += 1
        
        return {
            "total_size_bytes": total_size,
            "total_size_mb": total_size / 1024**2,
            "file_count": file_count
        }
    except Exception as e:
        return {
            "error": str(e)
        }
