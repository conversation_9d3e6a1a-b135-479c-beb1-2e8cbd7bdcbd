/* 全局样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

/* 代码字体 */
code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 录音组件样式 */
.audio-recorder {
  transition: all 0.3s ease;
}

.audio-recorder.recording {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.audio-recorder.paused {
  border-color: #faad14 !important;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
}

/* 音频可视化 */
.audio-visualizer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  background: linear-gradient(90deg, #f0f0f0, #e0e0e0);
  border-radius: 4px;
  overflow: hidden;
}

.audio-bar {
  width: 3px;
  margin: 0 1px;
  background: #1890ff;
  border-radius: 2px;
  transition: height 0.1s ease;
}

/* 会议卡片样式 */
.meeting-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.meeting-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.meeting-status-transcribing {
  color: #1890ff;
}

.meeting-status-pending {
  color: #faad14;
}

.meeting-status-completed {
  color: #52c41a;
}

/* 任务状态样式 */
.task-status-pending {
  color: #8c8c8c;
}

.task-status-in-progress {
  color: #1890ff;
}

.task-status-completed {
  color: #52c41a;
}

.task-status-cancelled {
  color: #ff4d4f;
}

/* 优先级样式 */
.priority-low {
  color: #52c41a;
}

.priority-medium {
  color: #faad14;
}

.priority-high {
  color: #ff4d4f;
}

/* 说话人标签样式 */
.speaker-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
  margin-bottom: 4px;
}

.speaker-tag-a {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.speaker-tag-b {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.speaker-tag-c {
  background: #fff2e8;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.speaker-tag-d {
  background: #fff0f6;
  color: #eb2f96;
  border: 1px solid #ffadd2;
}

/* 转写文本样式 */
.transcription-text {
  line-height: 1.8;
  font-size: 14px;
  color: #262626;
}

.transcription-segment {
  margin-bottom: 12px;
  padding: 8px 12px;
  border-left: 3px solid #f0f0f0;
  background: #fafafa;
  border-radius: 0 4px 4px 0;
}

.transcription-segment:hover {
  background: #f5f5f5;
  border-left-color: #1890ff;
}

.transcription-timestamp {
  font-size: 12px;
  color: #8c8c8c;
  margin-right: 8px;
  font-family: 'Courier New', monospace;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 999;
  }
  
  .ant-layout-content {
    margin-left: 0 !important;
  }
  
  .meeting-card {
    margin-bottom: 16px;
  }
  
  .audio-recorder .ant-statistic {
    text-align: center;
  }
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 脉冲动画 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 录音指示器 */
.recording-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: #ff4d4f;
  border-radius: 50%;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

/* 自定义按钮样式 */
.btn-recording {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border: none;
  color: white;
  animation: pulse 2s infinite;
}

.btn-recording:hover {
  background: linear-gradient(135deg, #ee5a24, #ff6b6b);
}

/* 音量条样式 */
.volume-bar {
  background: linear-gradient(90deg, #52c41a 0%, #faad14 50%, #ff4d4f 100%);
  height: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

/* 质量指示器 */
.quality-good {
  color: #52c41a;
}

.quality-fair {
  color: #faad14;
}

.quality-poor {
  color: #ff4d4f;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .ant-layout-sider {
    display: none !important;
  }
  
  .ant-layout-content {
    margin: 0 !important;
    padding: 20px !important;
  }
}
