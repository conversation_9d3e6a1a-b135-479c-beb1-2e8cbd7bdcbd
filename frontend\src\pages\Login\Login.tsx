import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { 
  Form, 
  Input, 
  Button, 
  Card, 
  Checkbox, 
  Alert, 
  Typography, 
  Space,
  Divider,
  Row,
  Col
} from 'antd';
import { 
  UserOutlined, 
  LockOutlined, 
  SoundOutlined,
  CheckCircleOutlined,
  SafetyOutlined,
  CloudOutlined
} from '@ant-design/icons';
import { useAuthStore } from '@/stores/authStore';
import { LoginRequest } from '@/types/api';

const { Title, Text, Paragraph } = Typography;

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [form] = Form.useForm();
  
  const { login, isLoading, error, clearError, isAuthenticated } = useAuthStore();
  const [rememberMe, setRememberMe] = useState(false);

  // 如果已经登录，重定向到目标页面
  useEffect(() => {
    if (isAuthenticated) {
      const from = (location.state as any)?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  // 清除错误信息
  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);

  const handleSubmit = async (values: LoginRequest) => {
    try {
      await login({
        ...values,
        remember: rememberMe,
      });
    } catch (error) {
      // 错误已经在store中处理
    }
  };

  const features = [
    {
      icon: <SoundOutlined style={{ color: '#1890ff' }} />,
      title: 'PC端实时录音',
      description: '浏览器内一键录音，实时质量监控'
    },
    {
      icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      title: '智能语音识别',
      description: '本地AI模型，零成本高精度转写'
    },
    {
      icon: <SafetyOutlined style={{ color: '#faad14' }} />,
      title: '多人发言识别',
      description: '自动区分说话人，统计发言活跃度'
    },
    {
      icon: <CloudOutlined style={{ color: '#722ed1' }} />,
      title: '智能纪要生成',
      description: 'AI自动生成结构化会议纪要'
    }
  ];

  return (
    <div style={{ 
      minHeight: '100vh', 
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Row gutter={[48, 0]} align="middle" style={{ maxWidth: 1200, width: '100%' }}>
        {/* 左侧：产品介绍 */}
        <Col xs={24} lg={12}>
          <div style={{ color: 'white', textAlign: 'center' }}>
            <Title level={1} style={{ color: 'white', marginBottom: 16 }}>
              🎤 智能会议系统
            </Title>
            <Paragraph style={{ color: 'rgba(255,255,255,0.8)', fontSize: 18, marginBottom: 32 }}>
              基于AI的智能会议管理平台，支持实时录音、语音转写、智能纪要生成
            </Paragraph>
            
            <Row gutter={[16, 24]}>
              {features.map((feature, index) => (
                <Col xs={24} sm={12} key={index}>
                  <Card 
                    size="small"
                    style={{ 
                      background: 'rgba(255,255,255,0.1)', 
                      border: 'none',
                      backdropFilter: 'blur(10px)'
                    }}
                    bodyStyle={{ padding: 16 }}
                  >
                    <Space direction="vertical" size={8} style={{ width: '100%' }}>
                      <div style={{ fontSize: 24 }}>{feature.icon}</div>
                      <Text strong style={{ color: 'white' }}>{feature.title}</Text>
                      <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: 12 }}>
                        {feature.description}
                      </Text>
                    </Space>
                  </Card>
                </Col>
              ))}
            </Row>
          </div>
        </Col>

        {/* 右侧：登录表单 */}
        <Col xs={24} lg={12}>
          <Card 
            style={{ 
              maxWidth: 400, 
              margin: '0 auto',
              boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
              borderRadius: 12
            }}
          >
            <div style={{ textAlign: 'center', marginBottom: 24 }}>
              <Title level={2} style={{ marginBottom: 8 }}>
                欢迎登录
              </Title>
              <Text type="secondary">
                请输入您的账号信息
              </Text>
            </div>

            {error && (
              <Alert
                message="登录失败"
                description={error}
                type="error"
                showIcon
                closable
                onClose={clearError}
                style={{ marginBottom: 16 }}
              />
            )}

            <Form
              form={form}
              name="login"
              onFinish={handleSubmit}
              autoComplete="off"
              size="large"
            >
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱地址' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="邮箱地址"
                  autoComplete="email"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码长度不能少于6位' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="密码"
                  autoComplete="current-password"
                />
              </Form.Item>

              <Form.Item>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Checkbox 
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                  >
                    记住我
                  </Checkbox>
                  <Link to="/forgot-password">
                    <Text type="secondary">忘记密码？</Text>
                  </Link>
                </div>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={isLoading}
                  block
                  style={{ height: 48 }}
                >
                  {isLoading ? '登录中...' : '登录'}
                </Button>
              </Form.Item>
            </Form>

            <Divider>
              <Text type="secondary">还没有账号？</Text>
            </Divider>

            <div style={{ textAlign: 'center' }}>
              <Link to="/register">
                <Button type="link" size="large">
                  立即注册
                </Button>
              </Link>
            </div>

            {/* 演示账号 */}
            <div style={{ marginTop: 16, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                💡 演示账号：<EMAIL> / 123456
              </Text>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Login;
