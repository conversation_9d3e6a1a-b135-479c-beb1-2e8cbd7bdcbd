#!/usr/bin/env python3
"""
智能会议系统集成测试脚本
测试前端、后端、AI服务的完整功能
"""

import asyncio
import aiohttp
import json
import time
import sys
import os
from pathlib import Path
from typing import Dict, List, Optional

class SystemTester:
    """系统测试器"""
    
    def __init__(self):
        self.frontend_url = "http://localhost:3000"
        self.backend_url = "http://localhost:8000"
        self.ai_service_url = "http://localhost:8001"
        
        self.session = None
        self.auth_token = None
        self.test_results = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def log_test(self, test_name: str, success: bool, message: str = "", duration: float = 0):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name} ({duration:.2f}s)")
        if message:
            print(f"    {message}")
        
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message,
            'duration': duration
        })
    
    async def test_service_health(self, service_name: str, url: str) -> bool:
        """测试服务健康状态"""
        start_time = time.time()
        try:
            async with self.session.get(f"{url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    duration = time.time() - start_time
                    self.log_test(f"{service_name} 健康检查", True, 
                                f"状态: {data.get('status', 'unknown')}", duration)
                    return True
                else:
                    duration = time.time() - start_time
                    self.log_test(f"{service_name} 健康检查", False, 
                                f"HTTP {response.status}", duration)
                    return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test(f"{service_name} 健康检查", False, str(e), duration)
            return False
    
    async def test_backend_auth(self) -> bool:
        """测试后端认证功能"""
        start_time = time.time()
        try:
            # 测试登录
            login_data = {
                "email": "<EMAIL>",
                "password": "123456"
            }
            
            async with self.session.post(
                f"{self.backend_url}/api/auth/login",
                json=login_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success') and data.get('data', {}).get('token'):
                        self.auth_token = data['data']['token']
                        duration = time.time() - start_time
                        self.log_test("后端用户登录", True, 
                                    f"用户: {data['data']['user']['name']}", duration)
                        return True
                    else:
                        duration = time.time() - start_time
                        self.log_test("后端用户登录", False, "响应格式错误", duration)
                        return False
                else:
                    duration = time.time() - start_time
                    self.log_test("后端用户登录", False, f"HTTP {response.status}", duration)
                    return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("后端用户登录", False, str(e), duration)
            return False
    
    async def test_meeting_crud(self) -> bool:
        """测试会议CRUD操作"""
        if not self.auth_token:
            self.log_test("会议CRUD测试", False, "需要先登录")
            return False
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        meeting_id = None
        
        try:
            # 创建会议
            start_time = time.time()
            meeting_data = {
                "title": "测试会议",
                "date": "2024-01-01T10:00:00Z",
                "customerName": "测试客户",
                "participants": ["张三", "李四"],
                "tags": ["测试", "演示"]
            }
            
            async with self.session.post(
                f"{self.backend_url}/api/meetings",
                json=meeting_data,
                headers=headers
            ) as response:
                if response.status == 201:
                    data = await response.json()
                    meeting_id = data['data']['id']
                    duration = time.time() - start_time
                    self.log_test("创建会议", True, f"会议ID: {meeting_id}", duration)
                else:
                    duration = time.time() - start_time
                    self.log_test("创建会议", False, f"HTTP {response.status}", duration)
                    return False
            
            # 获取会议详情
            start_time = time.time()
            async with self.session.get(
                f"{self.backend_url}/api/meetings/{meeting_id}",
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    duration = time.time() - start_time
                    self.log_test("获取会议详情", True, 
                                f"标题: {data['data']['title']}", duration)
                else:
                    duration = time.time() - start_time
                    self.log_test("获取会议详情", False, f"HTTP {response.status}", duration)
                    return False
            
            # 更新会议
            start_time = time.time()
            update_data = {"title": "更新后的测试会议"}
            async with self.session.put(
                f"{self.backend_url}/api/meetings/{meeting_id}",
                json=update_data,
                headers=headers
            ) as response:
                if response.status == 200:
                    duration = time.time() - start_time
                    self.log_test("更新会议", True, "", duration)
                else:
                    duration = time.time() - start_time
                    self.log_test("更新会议", False, f"HTTP {response.status}", duration)
                    return False
            
            # 删除会议
            start_time = time.time()
            async with self.session.delete(
                f"{self.backend_url}/api/meetings/{meeting_id}",
                headers=headers
            ) as response:
                if response.status == 200:
                    duration = time.time() - start_time
                    self.log_test("删除会议", True, "", duration)
                    return True
                else:
                    duration = time.time() - start_time
                    self.log_test("删除会议", False, f"HTTP {response.status}", duration)
                    return False
                    
        except Exception as e:
            self.log_test("会议CRUD测试", False, str(e))
            return False
    
    async def test_ai_transcription(self) -> bool:
        """测试AI转写功能"""
        start_time = time.time()
        try:
            # 测试转写服务状态
            async with self.session.get(f"{self.ai_service_url}/transcription/tasks") as response:
                if response.status == 200:
                    duration = time.time() - start_time
                    self.log_test("AI转写服务", True, "服务可用", duration)
                    return True
                else:
                    duration = time.time() - start_time
                    self.log_test("AI转写服务", False, f"HTTP {response.status}", duration)
                    return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("AI转写服务", False, str(e), duration)
            return False
    
    async def test_frontend_accessibility(self) -> bool:
        """测试前端可访问性"""
        start_time = time.time()
        try:
            async with self.session.get(self.frontend_url) as response:
                if response.status == 200:
                    content = await response.text()
                    if "智能会议系统" in content or "Smart Meeting" in content:
                        duration = time.time() - start_time
                        self.log_test("前端页面访问", True, "页面加载正常", duration)
                        return True
                    else:
                        duration = time.time() - start_time
                        self.log_test("前端页面访问", False, "页面内容异常", duration)
                        return False
                else:
                    duration = time.time() - start_time
                    self.log_test("前端页面访问", False, f"HTTP {response.status}", duration)
                    return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("前端页面访问", False, str(e), duration)
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始系统集成测试")
        print("=" * 50)
        
        # 测试服务健康状态
        print("\n📊 服务健康检查")
        print("-" * 30)
        backend_healthy = await self.test_service_health("后端服务", self.backend_url)
        ai_healthy = await self.test_service_health("AI服务", self.ai_service_url)
        frontend_accessible = await self.test_frontend_accessibility()
        
        # 测试后端功能
        print("\n🔧 后端功能测试")
        print("-" * 30)
        auth_success = False
        if backend_healthy:
            auth_success = await self.test_backend_auth()
            if auth_success:
                await self.test_meeting_crud()
        
        # 测试AI服务
        print("\n🤖 AI服务测试")
        print("-" * 30)
        if ai_healthy:
            await self.test_ai_transcription()
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n📋 测试报告")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n💡 建议:")
        if failed_tests == 0:
            print("  🎉 所有测试通过！系统运行正常。")
        else:
            print("  🔧 请检查失败的服务并重新启动。")
            print("  📚 查看日志文件获取更多错误信息。")
            print("  🆘 如需帮助，请查看快速开始指南。")

async def main():
    """主函数"""
    print("🎯 智能会议系统集成测试")
    print("测试前端、后端、AI服务的完整功能")
    print()
    
    async with SystemTester() as tester:
        await tester.run_all_tests()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 测试执行失败: {e}")
        sys.exit(1)
