import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { PrismaClient } from '@prisma/client';
import Redis from 'redis';

import { config } from './config/app.config';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/error.middleware';
import { authMiddleware } from './middleware/auth.middleware';

// 路由导入
import authRoutes from './routes/auth.routes';
import meetingRoutes from './routes/meeting.routes';
import taskRoutes from './routes/task.routes';
import hotwordRoutes from './routes/hotword.routes';

// 创建Express应用
const app = express();
const server = createServer(app);

// 创建Socket.IO服务器
const io = new SocketIOServer(server, {
  cors: {
    origin: config.cors.origin,
    methods: ['GET', 'POST'],
  },
});

// 数据库连接
export const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

// Redis连接
export const redis = Redis.createClient({
  url: config.redis.url,
});

// 中间件配置
app.use(helmet({
  crossOriginEmbedderPolicy: false,
}));

app.use(cors({
  origin: config.cors.origin,
  credentials: true,
}));

app.use(compression());
app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }));

// 请求体解析
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 静态文件服务
app.use('/uploads', express.static(config.upload.dir));

// 速率限制
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.max,
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试',
  },
});
app.use('/api', limiter);

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/meetings', authMiddleware, meetingRoutes);
app.use('/api/tasks', authMiddleware, taskRoutes);
app.use('/api/hotwords', authMiddleware, hotwordRoutes);

// Socket.IO连接处理
io.use((socket, next) => {
  // Socket.IO认证中间件
  const token = socket.handshake.auth.token;
  if (!token) {
    return next(new Error('Authentication error'));
  }
  
  // 这里应该验证token，简化处理
  socket.data.userId = 1; // 从token中解析用户ID
  next();
});

io.on('connection', (socket) => {
  logger.info(`用户连接: ${socket.id}`);
  
  // 加入用户房间
  const userId = socket.data.userId;
  socket.join(`user:${userId}`);
  
  // 录音相关事件
  socket.on('recording:start', async (data) => {
    logger.info(`用户 ${userId} 开始录音:`, data);
    // 处理录音开始逻辑
    socket.emit('recording:status', {
      status: 'started',
      timestamp: new Date().toISOString(),
    });
  });
  
  socket.on('recording:data', async (data) => {
    // 处理录音数据
    // 这里可以实时保存音频数据或进行实时转写
  });
  
  socket.on('recording:stop', async (data) => {
    logger.info(`用户 ${userId} 停止录音:`, data);
    // 处理录音停止逻辑
    socket.emit('recording:status', {
      status: 'stopped',
      timestamp: new Date().toISOString(),
    });
  });
  
  // 转写进度事件
  socket.on('transcription:subscribe', (meetingId) => {
    socket.join(`transcription:${meetingId}`);
  });
  
  socket.on('disconnect', () => {
    logger.info(`用户断开连接: ${socket.id}`);
  });
});

// 错误处理中间件
app.use(errorHandler);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
  });
});

// 数据库连接
async function connectDatabase() {
  try {
    await prisma.$connect();
    logger.info('数据库连接成功');
  } catch (error) {
    logger.error('数据库连接失败:', error);
    process.exit(1);
  }
}

// Redis连接
async function connectRedis() {
  try {
    await redis.connect();
    logger.info('Redis连接成功');
  } catch (error) {
    logger.error('Redis连接失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', async () => {
  logger.info('收到SIGTERM信号，开始优雅关闭...');
  
  server.close(() => {
    logger.info('HTTP服务器已关闭');
  });
  
  await prisma.$disconnect();
  await redis.disconnect();
  
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('收到SIGINT信号，开始优雅关闭...');
  
  server.close(() => {
    logger.info('HTTP服务器已关闭');
  });
  
  await prisma.$disconnect();
  await redis.disconnect();
  
  process.exit(0);
});

// 启动服务器
async function startServer() {
  try {
    await connectDatabase();
    await connectRedis();
    
    server.listen(config.port, () => {
      logger.info(`服务器启动成功，端口: ${config.port}`);
      logger.info(`环境: ${config.env}`);
      logger.info(`API文档: http://localhost:${config.port}/api-docs`);
    });
  } catch (error) {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 导出Socket.IO实例供其他模块使用
export { io };

// 如果直接运行此文件，则启动服务器
if (require.main === module) {
  startServer();
}

export default app;
