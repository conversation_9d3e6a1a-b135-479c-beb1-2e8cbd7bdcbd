# 智能会议系统需求说明文档 (SRS)

## Smart Meeting System - Requirements Specification 智能会议系统 - 需求规格说明

| 文档信息 | 详情                 |
| -------- | -------------------- |
| 文档版本 | V2.0                 |
| 项目代号 | SMS-2024             |
| 编写日期 | 2024-01-27           |
| 更新日期 | 2025-01-27           |
| 文档状态 | 正式版               |
| 目标交付 | 2024年4月（MVP版本） |

------

## 1. 项目概述

### 1.1 项目背景

在日常业务中，客户会议和内部会议产生大量有价值的信息，但由于缺乏有效的记录和管理工具，导致：

- 会议要点经常遗漏或记录不全
- 会后任务分配不明确，执行跟踪困难
- 历史会议信息难以检索和复用
- 手动整理会议纪要耗时费力

### 1.2 项目目标

构建一个简单、实用、可靠的会议管理系统，通过语音转写和智能提取技术，帮助用户：

1. **节省时间**：将会议纪要整理时间从60分钟降低到5分钟
2. **提升质量**：确保关键信息不遗漏，格式规范统一
3. **落实执行**：自动提取和跟踪会后任务，提升执行率

### 1.3 项目范围

**MVP版本包含**：

- 会议录音上传和转写
- **PC端实时录音和自动录入**
- 智能生成结构化纪要
- 任务提取和基础跟踪
- 简单的会议管理和搜索
- **语音质量检测和优化**
- **多人发言识别**

**MVP版本不包含**：

- 复杂的权限管理
- 移动端应用
- AI对话交互
- 数据分析报表
- 视频会议集成

### 1.4 用户角色定义

1. **普通用户**：创建会议、上传录音、编辑纪要、管理任务
2. **系统管理员**：管理用户账号、配置系统参数、维护词库

------

## 2. 功能需求

### 2.1 用户认证模块

#### 2.1.1 用户登录

**描述**：用户通过账号密码登录系统

**功能要求**：

- 支持邮箱/手机号 + 密码登录
- 登录失败5次后锁定账号30分钟
- 支持"记住我"功能（7天免登录）
- 忘记密码通过邮箱重置

**界面要求**：

text



```
登录页面布局：
┌─────────────────────────┐
│      智能会议系统        │
├─────────────────────────┤
│  邮箱/手机号：[_______]  │
│  密码：      [_______]  │
│  □ 记住我              │
│  [登录] [忘记密码?]     │
└─────────────────────────┘
```

#### 2.1.2 用户管理（管理员功能）

**功能要求**：

- 添加用户（姓名、邮箱、手机号、部门）
- 禁用/启用用户账号
- 重置用户密码
- 批量导入用户（Excel模板）

------

### 2.2 会议管理模块

#### 2.2.1 创建会议

**描述**：用户快速创建会议并录音或上传录音

**功能要求**：

- 必填字段：会议主题、会议日期
- 选填字段：客户名称、参会人员、会议标签
- **支持PC端实时录音**：
  - 一键开始/暂停/停止录音
  - 实时显示录音时长和音量
  - 支持录音质量检测（噪音、音量过低提醒）
  - 录音完成后自动保存并开始转写
- 支持上传音频文件（mp3/wav/m4a/aac，单文件≤500MB）
- 支持拖拽上传
- 创建后自动开始转写处理

**界面要求**：

text



```
创建会议表单：
┌─────────────────────────────────┐
│ 会议主题：[_______________] *    │
│ 会议日期：[2024-01-27 ▼] *     │
│ 客户名称：[_______________]      │
│ 参会人员：[_______________]      │
│ 会议标签：[产品讨论 ×] [+添加]   │
│                                 │
│ 录音方式：                      │
│ ○ 实时录音  ○ 上传文件          │
│                                 │
│ [实时录音模式]                  │
│ ┌─────────────────────┐        │
│ │ 🎤 [开始录音]        │        │
│ │ 录音时长: 00:00:00   │        │
│ │ 音量: ████████░░     │        │
│ │ 状态: 准备就绪       │        │
│ └─────────────────────┘        │
│                                 │
│ [上传文件模式]                  │
│ ┌─────────────────────┐        │
│ │  拖拽文件到此处       │        │
│ │  或点击选择文件       │        │
│ └─────────────────────┘        │
│                                 │
│ [取消] [创建会议]               │
└─────────────────────────────────┘
```

#### 2.2.2 会议列表

**功能要求**：

- 显示所有会议（分页，每页20条）
- 显示字段：主题、日期、客户、状态、操作
- 状态包括：转写中、待确认、已完成
- 支持按日期、客户、状态筛选
- 支持关键词搜索

#### 2.2.3 会议详情

**功能要求**：

- 显示会议基本信息
- 显示转写状态和进度
- 转写完成后显示完整文本
- 显示AI生成的结构化纪要
- 提供编辑和导出功能

------

### 2.3 PC端录音模块

#### 2.3.1 实时录音功能

**描述**：支持用户在PC端进行实时录音，自动录入会议内容

**功能要求**：

- **录音控制**：
  - 一键开始录音（检测麦克风权限）
  - 支持暂停/继续录音
  - 一键停止并保存录音
  - 录音过程中可随时取消

- **实时监控**：
  - 显示录音时长（HH:MM:SS格式）
  - 实时音量指示器（10级音量条）
  - 录音状态显示（准备、录音中、暂停、完成）
  - 音频质量检测（噪音过大、音量过低提醒）

- **录音优化**：
  - 自动降噪处理
  - 音量自动增益控制
  - 支持多种采样率（16kHz/44.1kHz/48kHz）
  - 自动检测静音段并标记

**界面要求**：

```
录音控制面板：
┌─────────────────────────────────┐
│ 🎤 会议录音                     │
├─────────────────────────────────┤
│ 状态：● 录音中                  │
│ 时长：01:23:45                  │
│ 音量：████████░░ (80%)          │
│ 质量：✓ 良好                    │
│                                 │
│ [⏸️ 暂停] [⏹️ 停止] [❌ 取消]    │
│                                 │
│ 💡 提示：请保持安静的环境       │
└─────────────────────────────────┘
```

#### 2.3.2 音频质量检测

**功能要求**：

- **噪音检测**：
  - 实时检测背景噪音水平
  - 噪音过大时显示警告提示
  - 建议用户调整环境或设备

- **音量检测**：
  - 检测说话音量是否合适
  - 音量过低/过高时提醒用户
  - 自动调整录音增益

- **设备检测**：
  - 自动检测可用麦克风设备
  - 支持切换录音设备
  - 设备异常时提供解决建议

#### 2.3.3 多人发言识别

**功能要求**：

- **说话人分离**：
  - 自动识别不同说话人
  - 为每个说话人分配标识（说话人A、B、C等）
  - 支持手动标记说话人姓名

- **发言时间统计**：
  - 统计每个说话人的发言时长
  - 显示发言时间占比
  - 生成发言活跃度报告

**技术实现**：

```javascript
// 录音功能实现示例
class AudioRecorder {
    constructor() {
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.isRecording = false;
    }

    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 44100
                }
            });

            this.mediaRecorder = new MediaRecorder(stream);
            this.setupRecorderEvents();
            this.mediaRecorder.start(1000); // 1秒间隔
            this.isRecording = true;

        } catch (error) {
            console.error('录音启动失败:', error);
            throw new Error('无法访问麦克风，请检查权限设置');
        }
    }

    setupRecorderEvents() {
        this.mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                this.audioChunks.push(event.data);
                this.analyzeAudioQuality(event.data);
            }
        };

        this.mediaRecorder.onstop = () => {
            this.processRecording();
        };
    }

    analyzeAudioQuality(audioData) {
        // 实时音频质量分析
        // 检测音量、噪音等
    }
}
```

------

### 2.4 语音转写模块

#### 2.4.1 音频转写

**功能要求**：

- **本地语音识别（主要方案）**：
  - 使用开源Whisper模型进行本地转写
  - 支持多语言模型（中文、英文、中英混合）
  - 离线运行，无需网络依赖，保护数据隐私
  - 支持GPU加速，提升转写速度

- **多模型API支持（备选方案）**：
  - 集成多个免费/开源语音识别API
  - 支持模型切换和负载均衡
  - 自动故障转移和模型选择
  - 成本控制和使用量监控

- **转写质量**：
  - 本地Whisper模型准确率≥92%（标准普通话）
  - API模型准确率≥88%（根据具体模型）
  - 1小时音频本地转写<5分钟（GPU加速）
  - 支持实时转写（延迟<3秒）

- **智能识别**：
  - 自动识别并高亮数字、日期、金额、电话号码
  - 自动识别专有名词（公司名、产品名、人名）
  - 支持自定义热词（客户名称、产品名称、专业术语）
  - 自动标点符号添加和语句分割

- **多人识别**：
  - 结合pyannote-audio进行说话人分离
  - 自动区分不同说话人
  - 为每个说话人生成独立文本段落
  - 支持说话人标签自定义和声纹学习

**技术实现**：

```python
import whisper
import torch
from pyannote.audio import Pipeline
import asyncio
from typing import List, Dict, Optional

class LocalASRService:
    def __init__(self):
        # 初始化Whisper模型
        self.whisper_model = whisper.load_model("large-v3")
        # 初始化说话人分离模型
        self.diarization_pipeline = Pipeline.from_pretrained(
            "pyannote/speaker-diarization-3.1"
        )
        # 检测GPU可用性
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

    def transcribe_with_speaker_diarization(self, audio_file: str) -> Dict:
        """本地转写 + 说话人分离"""
        try:
            # 1. 说话人分离
            diarization = self.diarization_pipeline(audio_file)

            # 2. Whisper转写
            result = self.whisper_model.transcribe(
                audio_file,
                language="zh",
                task="transcribe",
                fp16=False if self.device == "cpu" else True
            )

            # 3. 合并说话人信息和转写结果
            segments_with_speakers = self.merge_speaker_segments(
                result["segments"], diarization
            )

            return {
                "text": result["text"],
                "segments": segments_with_speakers,
                "language": result["language"],
                "speakers": self.extract_speakers(diarization)
            }

        except Exception as e:
            # 降级到API服务
            return self.fallback_to_api_service(audio_file)

    def fallback_to_api_service(self, audio_file: str) -> Dict:
        """API服务降级方案"""
        # 尝试多个免费API服务
        api_services = [
            self.try_openai_whisper_api,
            self.try_huggingface_api,
            self.try_local_whisper_cpp
        ]

        for api_service in api_services:
            try:
                result = api_service(audio_file)
                if result:
                    return result
            except Exception as e:
                continue

        raise Exception("所有转写服务都不可用")

class MultiModelASRManager:
    """多模型ASR管理器"""

    def __init__(self):
        self.local_service = LocalASRService()
        self.api_services = {
            "whisper_api": WhisperAPIService(),
            "huggingface": HuggingFaceASRService(),
            "local_whisper_cpp": WhisperCppService()
        }
        self.model_priority = ["local_whisper", "whisper_api", "huggingface"]

    async def transcribe(self, audio_file: str, prefer_local: bool = True) -> Dict:
        """智能选择最佳转写服务"""
        if prefer_local:
            try:
                return self.local_service.transcribe_with_speaker_diarization(audio_file)
            except Exception as e:
                print(f"本地转写失败，切换到API服务: {e}")

        # 尝试API服务
        for service_name in self.model_priority[1:]:
            try:
                service = self.api_services[service_name]
                result = await service.transcribe_async(audio_file)
                return result
            except Exception as e:
                continue

        raise Exception("所有转写服务都不可用")
```

#### 2.4.2 热词管理和模型优化

**功能要求**：

- **本地模型热词支持**：
  - 通过后处理方式增强Whisper识别效果
  - 基于编辑距离的热词纠错
  - 上下文语义匹配优化
  - 热词权重动态调整

- **热词管理**：
  - 系统级热词：所有用户共享的行业术语（上限1000个）
  - 用户级热词：用户自定义的客户名称、产品名称（上限500个）
  - 支持批量导入（txt/excel/json格式）
  - 热词使用频率统计和自动优化

- **多模型配置**：
  - 不同模型的热词适配策略
  - 模型切换时热词自动迁移
  - 热词效果评估和反馈机制

**技术实现**：

```python
class HotWordManager:
    """热词管理和优化"""

    def __init__(self):
        self.system_hotwords = self.load_system_hotwords()
        self.user_hotwords = {}
        self.hotword_embeddings = {}

    def enhance_transcription_with_hotwords(self, text: str, user_id: str) -> str:
        """使用热词增强转写结果"""
        user_hotwords = self.get_user_hotwords(user_id)
        all_hotwords = {**self.system_hotwords, **user_hotwords}

        enhanced_text = text

        # 1. 基于编辑距离的纠错
        enhanced_text = self.correct_with_edit_distance(enhanced_text, all_hotwords)

        # 2. 语义相似度匹配
        enhanced_text = self.correct_with_semantic_similarity(enhanced_text, all_hotwords)

        # 3. 上下文优化
        enhanced_text = self.context_aware_correction(enhanced_text, all_hotwords)

        return enhanced_text

    def correct_with_edit_distance(self, text: str, hotwords: Dict) -> str:
        """基于编辑距离的热词纠错"""
        import difflib

        words = text.split()
        corrected_words = []

        for word in words:
            best_match = difflib.get_close_matches(
                word, hotwords.keys(), n=1, cutoff=0.8
            )
            if best_match:
                corrected_words.append(hotwords[best_match[0]])
            else:
                corrected_words.append(word)

        return " ".join(corrected_words)

    def add_hotwords_batch(self, user_id: str, hotwords: List[str]) -> bool:
        """批量添加热词"""
        try:
            if user_id not in self.user_hotwords:
                self.user_hotwords[user_id] = {}

            for hotword in hotwords:
                if len(self.user_hotwords[user_id]) < 500:  # 用户热词上限
                    self.user_hotwords[user_id][hotword] = {
                        "weight": 1.0,
                        "usage_count": 0,
                        "created_at": datetime.now()
                    }

            self.save_user_hotwords(user_id)
            return True
        except Exception as e:
            return False
```

------

### 2.5 智能纪要生成模块

#### 2.5.1 结构化纪要生成

**描述**：基于转写文本，自动提取关键信息生成结构化纪要

**纪要模板**：

Markdown



```
# 会议纪要

**会议主题**：{会议主题}
**会议时间**：{会议日期}
**会议时长**：{录音时长}
**客户名称**：{客户名称}
**参会人员**：{参会人员}

## 一、参会人员发言统计
| 说话人 | 发言时长 | 发言占比 | 主要观点 |
|--------|----------|----------|----------|
| {说话人A} | {时长A} | {占比A} | {观点摘要A} |
| {说话人B} | {时长B} | {占比B} | {观点摘要B} |

## 二、客户需求与问题
1. {需求1} - 提出人：{说话人}
2. {需求2} - 提出人：{说话人}
3. {需求3} - 提出人：{说话人}

## 三、我方方案与承诺
1. {方案1} - 承诺人：{说话人}
2. {方案2} - 承诺人：{说话人}
3. {方案3} - 承诺人：{说话人}

## 四、待办事项
- [ ] {任务1} - 负责人：{人名} - 截止日期：{日期} - 来源：{会议时间点}
- [ ] {任务2} - 负责人：{人名} - 截止日期：{日期} - 来源：{会议时间点}

## 五、重要决策
1. {决策1} - 决策时间：{时间点}
2. {决策2} - 决策时间：{时间点}

## 六、下次沟通要点
- {要点1}
- {要点2}

## 七、会议录音
- 录音文件：{文件名}
- 转写准确率：{准确率}%
- 关键词标记：{标记数量}个
```

**提取规则**：

1. **客户需求识别**：
   - 关键词："需要"、"希望"、"要求"、"问题是"、"困难"、"挑战"
   - 提取包含这些关键词的句子，总结为需求点
2. **方案承诺识别**：
   - 关键词："我们会"、"我们将"、"可以提供"、"解决方案"、"承诺"
   - 提取我方的回应和承诺
3. **任务识别**：
   - 模式："{人名} + {动作} + {时间}"
   - 关键词："负责"、"完成"、"提交"、"准备"、"跟进"
4. **说话人分析**：
   - 自动统计每个说话人的发言时长和次数
   - 识别主要发言人和决策者
   - 分析发言活跃度和参与度
5. **决策识别**：
   - 关键词："决定"、"确定"、"同意"、"批准"、"通过"
   - 提取重要决策和结论

#### 2.5.2 纪要编辑

**功能要求**：

- 富文本编辑器（支持加粗、列表、标题）
- 实时保存（每30秒自动保存）
- 支持从原文快速定位（点击纪要内容跳转到原文位置）
- 一键应用模板重新生成

#### 2.5.3 纪要导出

**功能要求**：

- 导出格式：Word (.docx)、PDF、Markdown、Excel
- 包含公司logo和标准页眉页脚
- 支持批量导出多个会议纪要
- **新增功能**：
  - 导出时包含说话人统计图表
  - 支持导出音频文件链接
  - 可选择导出原始转写文本
  - 支持自定义导出模板

------

### 2.6 任务管理模块

#### 2.6.1 任务提取与创建

**功能要求**：

- 从纪要中自动提取任务
- 手动创建任务
- 任务字段：标题、描述、负责人、截止日期、状态
- 状态：待处理、进行中、已完成、已取消

#### 2.6.2 任务列表

**功能要求**：

- "我的任务"视图：显示分配给当前用户的任务
- "所有任务"视图：显示用户创建的所有任务
- 按状态、截止日期筛选
- 逾期任务红色高亮显示
- **新增功能**：
  - 任务来源会议链接（可直接跳转到原会议）
  - 任务优先级设置（高、中、低）
  - 任务进度跟踪（百分比显示）

#### 2.6.3 任务提醒

**功能要求**：

- 任务创建时邮件通知负责人
- 截止日期前1天邮件提醒
- 逾期任务每天提醒一次（最多3次）

**邮件模板**：

text



```
主题：[智能会议系统] 您有新的任务待处理

{负责人}，您好：

您有一项新任务需要处理：
任务内容：{任务标题}
来源会议：{会议主题}
截止日期：{截止日期}
任务详情：{任务描述}

请登录系统查看详情：{系统链接}
```

------

### 2.7 搜索功能

#### 2.7.1 全局搜索

**功能要求**：

- **搜索范围**：
  - 会议标题、客户名称、纪要内容、原文内容
  - 说话人姓名和发言内容
  - 任务标题和描述
  - 会议标签和关键词

- **搜索功能**：
  - 支持模糊搜索和精确搜索
  - 支持语音搜索（说出关键词进行搜索）
  - 搜索结果高亮显示匹配内容
  - 按相关度、时间、客户等多维度排序
  - 支持搜索历史记录
  - 智能搜索建议（输入时自动提示）

#### 2.7.2 高级搜索

**功能要求**：

- **时间范围搜索**：按日期区间筛选
- **说话人搜索**：按特定说话人筛选内容
- **客户维度搜索**：按客户分组显示结果
- **内容类型搜索**：区分需求、方案、任务、决策等
- **组合搜索**：支持多条件组合查询

------

## 3. 非功能需求

### 3.1 性能要求

- **页面性能**：
  - 页面加载时间 < 3秒
  - 系统响应时间 < 1秒（除文件上传和转写）
  - 并发支持：100用户同时在线

- **录音性能**：
  - PC录音延迟 < 100ms
  - 录音质量：44.1kHz/16bit 或更高
  - 实时音频处理不影响录音质量
  - 支持连续录音4小时不中断

- **转写性能**：
  - 音频上传速度 ≥ 5MB/s
  - 本地转写速度：1小时音频 < 5分钟（GPU）/ < 15分钟（CPU）
  - API转写速度：1小时音频 < 3分钟（取决于网络）
  - 实时转写延迟 < 3秒（本地）/ < 2秒（API）
  - 多人识别准确率 ≥ 88%（本地）/ ≥ 85%（API）
  - 模型加载时间 < 30秒（首次启动）

- **存储性能**：
  - 音频文件压缩率 ≥ 50%（不影响质量）
  - 数据库查询响应 < 500ms
  - 搜索结果返回 < 2秒

### 3.2 可靠性要求

- **系统可靠性**：
  - 系统可用性 ≥ 99.5%
  - 数据备份：每日自动备份，异地备份
  - 转写失败自动重试（最多3次）
  - 断点续传支持

- **录音可靠性**：
  - 录音过程异常自动恢复
  - 网络中断时本地缓存录音数据
  - 设备切换时自动适配
  - 录音数据实时校验，防止损坏

### 3.3 安全要求

- **数据安全**：
  - HTTPS加密传输
  - 音频文件AES-256加密存储
  - 敏感信息脱敏处理
  - 定期安全漏洞扫描

- **访问安全**：
  - 登录会话超时：30分钟无操作自动登出
  - 操作日志记录（登录、创建、修改、删除）
  - 麦克风权限管理和提醒
  - 录音数据访问权限控制

### 3.4 兼容性要求

- **浏览器兼容性**：
  - Chrome 80+、Firefox 75+、Safari 13+、Edge 80+
  - 支持WebRTC和MediaRecorder API
  - 自动检测浏览器录音能力

- **设备兼容性**：
  - 屏幕分辨率：最低1366×768，支持响应式设计
  - 音频格式：MP3、WAV、M4A、AAC、FLAC
  - 麦克风设备：USB麦克风、内置麦克风、蓝牙耳机
  - 操作系统：Windows 10+、macOS 10.15+、Linux（主流发行版）

### 3.5 易用性要求

- **操作简便性**：
  - 无需培训即可上手使用
  - 关键操作不超过3次点击
  - 录音功能一键启动，操作直观
  - 提供快捷键支持（空格键开始/暂停录音）

- **用户体验**：
  - 错误信息清晰明确，提供解决方案
  - 提供操作引导和帮助文档
  - 录音状态实时反馈，用户感知良好
  - 支持暗色/亮色主题切换

- **辅助功能**：
  - 录音前自动检测设备状态
  - 提供录音质量实时提示
  - 智能推荐最佳录音设置
  - 新手引导和功能介绍

------

## 4. 技术架构设计

### 4.1 整体架构

text



```
┌─────────────────────────────────────────────────────────┐
│                    前端 (React)                          │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐      │
│  │登录模块  │ │会议管理  │ │纪要编辑  │ │任务管理  │      │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘      │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐      │
│  │录音模块  │ │转写模块  │ │搜索模块  │ │说话人识别│      │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘      │
└─────────────────────────────────────────────────────────┘
                            │
                    HTTPS REST API + WebSocket
                            │
┌─────────────────────────────────────────────────────────┐
│                   API网关 (Nginx)                        │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                  应用服务层 (Node.js)                    │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐      │
│  │用户服务  │ │会议服务  │ │转写服务  │ │任务服务  │      │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘      │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐      │
│  │录音服务  │ │AI分析   │ │搜索服务  │ │通知服务  │      │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘      │
└─────────────────────────────────────────────────────────┘
         │           │           │           │
         └───────────┴───────────┴───────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                   数据存储层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │MySQL数据库   │ │Redis缓存    │ │文件存储OSS   │      │
│  │(会议/用户)   │ │(会话/热词)  │ │(音频/文档)   │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                   本地AI服务层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │Whisper模型   │ │说话人分离   │ │热词优化     │      │
│  │(本地部署)   │ │(pyannote)   │ │(后处理)     │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                   第三方服务（备选）                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │OpenAI API   │ │HuggingFace  │ │邮件服务     │      │
│  │(Whisper)    │ │(免费模型)   │ │(阿里云)     │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
└─────────────────────────────────────────────────────────┘
```

### 4.2 PC录音技术架构

```
┌─────────────────────────────────────────────────────────┐
│                    浏览器端录音架构                       │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │MediaRecorder│ │AudioContext │ │WebRTC API   │      │
│  │录音控制     │ │音频处理     │ │设备管理     │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
│                            │                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │音量检测     │ │噪音分析     │ │质量监控     │      │
│  │VolumeAnalyzer│ │NoiseDetector│ │QualityMonitor│     │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
│                            │                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │实时上传     │ │断点续传     │ │本地缓存     │      │
│  │StreamUpload │ │ResumeUpload │ │LocalCache   │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
└─────────────────────────────────────────────────────────┘
                            │
                      WebSocket连接
                            │
┌─────────────────────────────────────────────────────────┐
│                    服务端录音处理                         │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │音频接收     │ │格式转换     │ │质量检测     │      │
│  │AudioReceiver│ │FormatConverter│ │QualityChecker│    │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
│                            │                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │说话人分离   │ │实时转写     │ │智能分析     │      │
│  │SpeakerSep   │ │RealTimeASR  │ │AIAnalyzer   │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
└─────────────────────────────────────────────────────────┘
```

------

## 5. PC录音功能详细设计

### 5.1 录音流程设计

**录音启动流程**：
1. 检测浏览器录音API支持情况
2. 请求麦克风权限
3. 检测可用录音设备
4. 初始化录音参数（采样率、声道等）
5. 开始录音并实时监控

**录音过程管理**：
1. 实时音频数据采集（1秒间隔）
2. 音频质量实时分析
3. 数据分片上传（避免大文件传输）
4. 录音状态实时同步
5. 异常情况自动处理

**录音结束流程**：
1. 停止音频采集
2. 完成剩余数据上传
3. 生成完整音频文件
4. 触发转写任务
5. 释放系统资源

### 5.2 音频质量优化

**噪音处理**：
- 使用Web Audio API进行实时降噪
- 自适应噪音门限调整
- 背景噪音检测和提醒

**音量优化**：
- 自动增益控制（AGC）
- 音量过低/过高实时提醒
- 动态范围压缩

**音频增强**：
- 回声消除（AEC）
- 语音增强算法
- 频率响应优化

### 5.3 多人识别技术

**说话人分离算法**：
- 基于声纹特征的说话人聚类
- 实时说话人切换检测
- 说话人标签自动分配

**发言统计分析**：
- 每个说话人发言时长统计
- 发言频次和活跃度分析
- 发言重叠检测和处理

### 5.4 实时转写优化

**流式转写**：
- 音频流实时传输
- 增量转写结果返回
- 转写结果实时校正

**准确率提升**：
- 用户自定义热词实时加载
- 上下文语义理解
- 多模型融合识别

------

## 6. 本地AI模型部署方案

### 6.1 模型选择和配置

**Whisper模型配置**：
```python
# 模型配置文件 config/whisper_config.py
WHISPER_CONFIG = {
    "model_size": "large-v3",  # tiny, base, small, medium, large, large-v3
    "language": "zh",          # 主要语言
    "device": "auto",          # auto, cpu, cuda
    "compute_type": "float16", # float16, float32, int8
    "beam_size": 5,            # 解码束搜索大小
    "best_of": 5,              # 候选数量
    "temperature": 0.0,        # 随机性控制
    "compression_ratio_threshold": 2.4,
    "logprob_threshold": -1.0,
    "no_speech_threshold": 0.6
}

# 根据硬件自动选择配置
def auto_select_config():
    import torch
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory
        if gpu_memory > 8 * 1024**3:  # 8GB+
            return "large-v3"
        elif gpu_memory > 4 * 1024**3:  # 4GB+
            return "medium"
        else:
            return "base"
    else:
        import psutil
        ram = psutil.virtual_memory().total
        if ram > 16 * 1024**3:  # 16GB+
            return "medium"
        else:
            return "base"
```

**说话人分离模型配置**：
```python
# 说话人分离配置
DIARIZATION_CONFIG = {
    "model": "pyannote/speaker-diarization-3.1",
    "min_speakers": 1,
    "max_speakers": 8,
    "segmentation_onset": 0.5,
    "segmentation_offset": 0.5,
    "clustering_threshold": 0.7
}
```

### 6.2 模型部署架构

**容器化部署**：
```dockerfile
# Dockerfile for AI Service
FROM nvidia/cuda:11.8-runtime-ubuntu20.04

# 安装Python和依赖
RUN apt-get update && apt-get install -y python3 python3-pip ffmpeg
COPY requirements.txt .
RUN pip3 install -r requirements.txt

# 下载和缓存模型
RUN python3 -c "import whisper; whisper.load_model('large-v3')"
RUN python3 -c "from pyannote.audio import Pipeline; Pipeline.from_pretrained('pyannote/speaker-diarization-3.1')"

# 复制应用代码
COPY . /app
WORKDIR /app

# 启动服务
CMD ["python3", "ai_service.py"]
```

**Docker Compose配置**：
```yaml
version: '3.8'
services:
  ai-service:
    build: ./ai-service
    ports:
      - "8001:8001"
    volumes:
      - ./models:/app/models
      - ./audio_files:/app/audio_files
    environment:
      - CUDA_VISIBLE_DEVICES=0
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  web-service:
    build: ./web-service
    ports:
      - "3000:3000"
    depends_on:
      - ai-service
      - database

  database:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: meeting_system
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  mysql_data:
```

### 6.3 模型管理和更新

**模型版本管理**：
```python
class ModelManager:
    def __init__(self):
        self.model_registry = {
            "whisper": {
                "current": "large-v3",
                "available": ["tiny", "base", "small", "medium", "large", "large-v3"],
                "path": "/app/models/whisper/"
            },
            "diarization": {
                "current": "pyannote/speaker-diarization-3.1",
                "available": ["pyannote/speaker-diarization-3.1"],
                "path": "/app/models/diarization/"
            }
        }

    def update_model(self, model_type: str, version: str):
        """更新模型版本"""
        if version in self.model_registry[model_type]["available"]:
            # 下载新模型
            self.download_model(model_type, version)
            # 热更新
            self.hot_reload_model(model_type, version)
            # 更新配置
            self.model_registry[model_type]["current"] = version

    def health_check(self):
        """模型健康检查"""
        return {
            "whisper": self.check_whisper_model(),
            "diarization": self.check_diarization_model(),
            "gpu_available": torch.cuda.is_available(),
            "memory_usage": self.get_memory_usage()
        }
```

------

## 7. 部署和运维要求

### 6.1 服务器配置要求

**最低配置（仅API模式）**：
- CPU: 4核心 2.4GHz
- 内存: 8GB RAM
- 存储: 500GB SSD
- 带宽: 100Mbps
- GPU: 无需求

**推荐配置（本地AI + API混合）**：
- CPU: 8核心 3.0GHz
- 内存: 32GB RAM
- 存储: 1TB NVMe SSD + 2TB HDD
- 带宽: 1Gbps
- GPU: NVIDIA RTX 4060 (8GB VRAM) 或更高

**高性能配置（纯本地AI）**：
- CPU: 16核心 3.5GHz
- 内存: 64GB RAM
- 存储: 2TB NVMe SSD + 4TB HDD
- 带宽: 10Gbps
- GPU: NVIDIA RTX 4080/4090 (16GB+ VRAM)

**本地AI模型存储需求**：
- Whisper Large-v3: ~3GB
- 说话人分离模型: ~1GB
- 热词词典和索引: ~500MB
- 模型缓存空间: ~10GB

### 6.2 AI模型和服务依赖

**本地AI模型（主要方案）**：
- OpenAI Whisper (开源免费)
- pyannote-audio 说话人分离 (开源免费)
- 自研热词优化算法
- 本地部署，无API调用成本

**备选API服务（降级方案）**：
- OpenAI Whisper API (按使用量付费)
- HuggingFace Inference API (部分免费)
- Google Speech-to-Text (免费额度)
- Azure Speech Services (免费额度)

**AI大模型服务（纪要生成）**：
- 本地部署的开源模型（如Qwen、ChatGLM）
- OpenAI GPT-4 API (备选)
- 百度文心一言 API (备选)
- 阿里云通义千问 API (备选)

**存储服务**：
- 本地文件系统存储 (主要)
- 阿里云OSS对象存储 (备选)
- 腾讯云COS对象存储 (备选)
- MinIO自建对象存储 (推荐)

**成本优势**：
- 本地模型零API调用费用
- 数据完全本地化，隐私安全
- 不受网络限制，稳定可靠
- 一次部署，长期使用

### 6.3 监控和告警

**系统监控**：
- 服务器性能监控
- 应用服务状态监控
- 数据库性能监控
- 网络连接质量监控

**业务监控**：
- 录音成功率监控
- 转写准确率监控
- 用户活跃度监控
- 功能使用情况统计

**告警机制**：
- 系统异常邮件告警
- 关键指标阈值告警
- 用户反馈问题告警
- 定期运营报告

------

## 8. 项目里程碑和交付计划

### 8.1 开发阶段规划

**第一阶段（4周）- 基础功能开发**：
- 用户认证和会议管理
- 文件上传和基础转写（API模式）
- 简单纪要生成

**第二阶段（5周）- 本地AI模型集成**：
- Whisper模型本地部署和优化
- 说话人分离模型集成
- 热词管理和后处理优化
- 模型性能测试和调优

**第三阶段（6周）- PC录音功能开发**：
- 浏览器录音功能实现
- 实时音频质量监控
- 录音数据上传和处理
- 实时转写功能开发

**第四阶段（4周）- 智能分析功能**：
- 多人识别和说话人分离优化
- 智能纪要生成增强
- 任务自动提取和分类
- 多模型API集成和切换

**第五阶段（3周）- 性能优化和测试**：
- GPU加速优化
- 模型推理性能调优
- 并发处理能力测试
- 用户体验优化

**第六阶段（2周）- 部署和上线**：
- 容器化部署配置
- 生产环境部署
- 监控和告警配置
- 用户培训和文档

### 7.2 质量保证

**测试策略**：
- 单元测试覆盖率 ≥ 80%
- 集成测试和端到端测试
- 性能测试和压力测试
- 兼容性测试（多浏览器、多设备）

**验收标准**：
- 功能完整性验收
- 性能指标达标验收
- 安全性评估通过
- 用户体验满意度 ≥ 85%

------

## 8. 风险评估和应对策略

### 8.1 技术风险

**浏览器兼容性风险**：
- 风险：不同浏览器录音API差异
- 应对：多浏览器适配和降级方案

**音频质量风险**：
- 风险：网络环境影响录音质量
- 应对：本地缓存和质量检测机制

**转写准确率风险**：
- 风险：方言、噪音影响识别准确率
- 应对：多服务商备选和人工校正

### 8.2 业务风险

**用户接受度风险**：
- 风险：用户不习惯PC录音方式
- 应对：详细使用指导和客服支持

**数据安全风险**：
- 风险：录音数据泄露
- 应对：加密存储和访问控制

**服务稳定性风险**：
- 风险：高并发时系统不稳定
- 应对：负载均衡和弹性扩容

------

## 10. 总结

本次需求文档优化主要增加了以下核心功能和技术方案：

### 核心功能优化：
1. **PC端实时录音功能**：支持浏览器内一键录音，实时质量监控
2. **多人发言识别**：自动区分说话人，统计发言时长和活跃度
3. **智能纪要优化**：增加说话人信息、发言统计、决策记录
4. **音频质量检测**：实时噪音检测、音量监控、设备适配
5. **高级搜索功能**：支持语音搜索、多维度筛选、智能建议

### 技术架构革新：
6. **本地AI模型部署**：使用免费开源Whisper模型，零API调用成本
7. **多模型API支持**：集成多个免费/开源语音识别API作为备选
8. **智能模型切换**：自动故障转移和最佳模型选择
9. **热词优化系统**：基于编辑距离和语义相似度的后处理优化
10. **容器化部署**：Docker容器化，支持GPU加速，易于扩展

### 成本和性能优势：
- **零API调用费用**：本地模型完全免费使用
- **数据隐私保护**：音频数据不离开本地服务器
- **高可用性**：不依赖外部网络，稳定可靠
- **灵活扩展**：支持GPU加速，性能可按需提升
- **多重保障**：本地+API双重保障，确保服务连续性

### 技术特色：
- **开源优先**：优先使用开源免费模型和工具
- **智能降级**：本地模型失败时自动切换到API服务
- **性能优化**：GPU加速、模型缓存、并发处理
- **易于维护**：容器化部署、模型版本管理、健康检查

这些优化将显著提升系统的易用性、智能化程度和用户体验，同时大幅降低运营成本，使其成为一个真正实用且经济的智能会议管理系统。