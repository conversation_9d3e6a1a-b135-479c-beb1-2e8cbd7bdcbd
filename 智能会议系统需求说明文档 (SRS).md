# 智能会议系统需求说明文档 (SRS)

## Smart Meeting System - Requirements Specification 智能会议系统 - 需求规格说明

| 文档信息 | 详情                 |
| -------- | -------------------- |
| 文档版本 | V1.0                 |
| 项目代号 | SMS-2024             |
| 编写日期 | 2024-01-27           |
| 文档状态 | 正式版               |
| 目标交付 | 2024年4月（MVP版本） |

------

## 1. 项目概述

### 1.1 项目背景

在日常业务中，客户会议和内部会议产生大量有价值的信息，但由于缺乏有效的记录和管理工具，导致：

- 会议要点经常遗漏或记录不全
- 会后任务分配不明确，执行跟踪困难
- 历史会议信息难以检索和复用
- 手动整理会议纪要耗时费力

### 1.2 项目目标

构建一个简单、实用、可靠的会议管理系统，通过语音转写和智能提取技术，帮助用户：

1. **节省时间**：将会议纪要整理时间从60分钟降低到5分钟
2. **提升质量**：确保关键信息不遗漏，格式规范统一
3. **落实执行**：自动提取和跟踪会后任务，提升执行率

### 1.3 项目范围

**MVP版本包含**：

- 会议录音上传和转写
- 智能生成结构化纪要
- 任务提取和基础跟踪
- 简单的会议管理和搜索

**MVP版本不包含**：

- 实时会议转写
- 复杂的权限管理
- 移动端应用
- AI对话交互
- 数据分析报表

### 1.4 用户角色定义

1. **普通用户**：创建会议、上传录音、编辑纪要、管理任务
2. **系统管理员**：管理用户账号、配置系统参数、维护词库

------

## 2. 功能需求

### 2.1 用户认证模块

#### 2.1.1 用户登录

**描述**：用户通过账号密码登录系统

**功能要求**：

- 支持邮箱/手机号 + 密码登录
- 登录失败5次后锁定账号30分钟
- 支持"记住我"功能（7天免登录）
- 忘记密码通过邮箱重置

**界面要求**：

text



```
登录页面布局：
┌─────────────────────────┐
│      智能会议系统        │
├─────────────────────────┤
│  邮箱/手机号：[_______]  │
│  密码：      [_______]  │
│  □ 记住我              │
│  [登录] [忘记密码?]     │
└─────────────────────────┘
```

#### 2.1.2 用户管理（管理员功能）

**功能要求**：

- 添加用户（姓名、邮箱、手机号、部门）
- 禁用/启用用户账号
- 重置用户密码
- 批量导入用户（Excel模板）

------

### 2.2 会议管理模块

#### 2.2.1 创建会议

**描述**：用户快速创建会议并上传录音

**功能要求**：

- 必填字段：会议主题、会议日期
- 选填字段：客户名称、参会人员、会议标签
- 支持上传音频文件（mp3/wav/m4a，单文件≤500MB）
- 支持拖拽上传
- 创建后自动开始转写处理

**界面要求**：

text



```
创建会议表单：
┌─────────────────────────────────┐
│ 会议主题：[_______________] *    │
│ 会议日期：[2024-01-27 ▼] *     │
│ 客户名称：[_______________]      │
│ 参会人员：[_______________]      │
│ 会议标签：[产品讨论 ×] [+添加]   │
│                                 │
│ 上传录音：                      │
│ ┌─────────────────────┐        │
│ │  拖拽文件到此处       │        │
│ │  或点击选择文件       │        │
│ └─────────────────────┘        │
│                                 │
│ [取消] [创建会议]               │
└─────────────────────────────────┘
```

#### 2.2.2 会议列表

**功能要求**：

- 显示所有会议（分页，每页20条）
- 显示字段：主题、日期、客户、状态、操作
- 状态包括：转写中、待确认、已完成
- 支持按日期、客户、状态筛选
- 支持关键词搜索

#### 2.2.3 会议详情

**功能要求**：

- 显示会议基本信息
- 显示转写状态和进度
- 转写完成后显示完整文本
- 显示AI生成的结构化纪要
- 提供编辑和导出功能

------

### 2.3 语音转写模块

#### 2.3.1 音频转写

**功能要求**：

- 自动识别中文普通话
- 转写准确率≥85%（普通话标准发音）
- 1小时音频在5分钟内完成转写
- 自动识别并高亮数字、日期、金额
- 支持自定义热词（客户名称、产品名称、专业术语）

**技术实现**：

Python



```
# 转写任务处理流程
def process_audio_transcription(audio_file):
    # 1. 验证音频格式
    validate_audio_format(audio_file)
    
    # 2. 上传到ASR服务
    task_id = asr_service.create_task(
        audio_file=audio_file,
        language='zh-CN',
        hot_words=get_user_hotwords()
    )
    
    # 3. 轮询获取结果
    result = asr_service.get_result(task_id)
    
    # 4. 后处理优化
    optimized_text = post_process(result.text)
    
    return optimized_text
```

#### 2.3.2 热词管理

**功能要求**：

- 系统级热词：所有用户共享的行业术语
- 用户级热词：用户自定义的客户名称、产品名称
- 支持批量导入（txt/excel格式）
- 热词上限：系统级500个，用户级200个

------

### 2.4 智能纪要生成模块

#### 2.4.1 结构化纪要生成

**描述**：基于转写文本，自动提取关键信息生成结构化纪要

**纪要模板**：

Markdown



```
# 会议纪要

**会议主题**：{会议主题}
**会议时间**：{会议日期}
**客户名称**：{客户名称}
**参会人员**：{参会人员}

## 一、客户需求与问题
1. {需求1}
2. {需求2}
3. {需求3}

## 二、我方方案与承诺
1. {方案1}
2. {方案2}
3. {方案3}

## 三、待办事项
- [ ] {任务1} - 负责人：{人名} - 截止日期：{日期}
- [ ] {任务2} - 负责人：{人名} - 截止日期：{日期}

## 四、下次沟通要点
- {要点1}
- {要点2}
```

**提取规则**：

1. **客户需求识别**：
   - 关键词："需要"、"希望"、"要求"、"问题是"、"困难"、"挑战"
   - 提取包含这些关键词的句子，总结为需求点
2. **方案承诺识别**：
   - 关键词："我们会"、"我们将"、"可以提供"、"解决方案"、"承诺"
   - 提取我方的回应和承诺
3. **任务识别**：
   - 模式："{人名} + {动作} + {时间}"
   - 关键词："负责"、"完成"、"提交"、"准备"、"跟进"

#### 2.4.2 纪要编辑

**功能要求**：

- 富文本编辑器（支持加粗、列表、标题）
- 实时保存（每30秒自动保存）
- 支持从原文快速定位（点击纪要内容跳转到原文位置）
- 一键应用模板重新生成

#### 2.4.3 纪要导出

**功能要求**：

- 导出格式：Word (.docx)、PDF、Markdown
- 包含公司logo和标准页眉页脚
- 支持批量导出多个会议纪要

------

### 2.5 任务管理模块

#### 2.5.1 任务提取与创建

**功能要求**：

- 从纪要中自动提取任务
- 手动创建任务
- 任务字段：标题、描述、负责人、截止日期、状态
- 状态：待处理、进行中、已完成、已取消

#### 2.5.2 任务列表

**功能要求**：

- "我的任务"视图：显示分配给当前用户的任务
- "所有任务"视图：显示用户创建的所有任务
- 按状态、截止日期筛选
- 逾期任务红色高亮显示

#### 2.5.3 任务提醒

**功能要求**：

- 任务创建时邮件通知负责人
- 截止日期前1天邮件提醒
- 逾期任务每天提醒一次（最多3次）

**邮件模板**：

text



```
主题：[智能会议系统] 您有新的任务待处理

{负责人}，您好：

您有一项新任务需要处理：
任务内容：{任务标题}
来源会议：{会议主题}
截止日期：{截止日期}
任务详情：{任务描述}

请登录系统查看详情：{系统链接}
```

------

### 2.6 搜索功能

#### 2.6.1 全局搜索

**功能要求**：

- 搜索范围：会议标题、客户名称、纪要内容、原文内容
- 支持模糊搜索
- 搜索结果高亮显示匹配内容
- 按相关度排序

------

## 3. 非功能需求

### 3.1 性能要求

- 页面加载时间 < 3秒
- 音频上传速度 ≥ 5MB/s
- 转写速度：1小时音频 < 5分钟
- 并发支持：100用户同时在线
- 系统响应时间 < 1秒（除文件上传和转写）

### 3.2 可靠性要求

- 系统可用性 ≥ 99%
- 数据备份：每日自动备份
- 转写失败自动重试（最多3次）
- 断点续传支持

### 3.3 安全要求

- HTTPS加密传输
- 音频文件加密存储
- 登录会话超时：30分钟无操作自动登出
- 操作日志记录（登录、创建、修改、删除）

### 3.4 兼容性要求

- 浏览器：Chrome 80+、Firefox 75+、Safari 13+、Edge 80+
- 屏幕分辨率：最低1366×768
- 音频格式：MP3、WAV、M4A、AAC

### 3.5 易用性要求

- 无需培训即可上手使用
- 关键操作不超过3次点击
- 错误信息清晰明确
- 提供操作引导和帮助文档

------

## 4. 技术架构设计

### 4.1 整体架构

text



```
┌─────────────────────────────────────────────────────────┐
│                      前端 (React)                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐      │
│  │登录模块  │ │会议管理  │ │纪要编辑  │ │任务管理  │      │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘      │
└─────────────────────────────────────────────────────────┘
                            │
                    HTTPS REST API
                            │
┌─────────────────────────────────────────────────────────┐
│                   API网关 (Nginx)                        │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                  应用服务层 (Node.js)                    │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐      │
│  │用户服务  │ │会议服务  │ │转写服务  │ │任务服务  │      │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘      │
└─────────────────────────────────────────────────────────┘
         │           │           │           │
         └───────────┴───────────┴───────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                   数据存储层                             │
│  ┌─────────────┐ 
```