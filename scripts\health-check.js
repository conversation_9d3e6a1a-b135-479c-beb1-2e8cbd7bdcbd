#!/usr/bin/env node
/**
 * 系统健康检查脚本
 * 检查所有服务的运行状态
 */

const http = require('http');
const https = require('https');

// 服务配置
const services = [
  {
    name: '前端服务',
    url: 'http://localhost:3000',
    timeout: 5000,
    critical: true
  },
  {
    name: '后端服务',
    url: 'http://localhost:8000/health',
    timeout: 5000,
    critical: true
  },
  {
    name: 'AI服务',
    url: 'http://localhost:8001/health',
    timeout: 10000,
    critical: true
  },
  {
    name: 'MySQL数据库',
    url: 'http://localhost:8000/api/health/db',
    timeout: 5000,
    critical: true
  },
  {
    name: 'Redis缓存',
    url: 'http://localhost:8000/api/health/redis',
    timeout: 5000,
    critical: false
  }
];

/**
 * 检查单个服务
 */
function checkService(service) {
  return new Promise((resolve) => {
    const url = new URL(service.url);
    const client = url.protocol === 'https:' ? https : http;
    
    const startTime = Date.now();
    
    const req = client.get(service.url, {
      timeout: service.timeout
    }, (res) => {
      const responseTime = Date.now() - startTime;
      
      if (res.statusCode >= 200 && res.statusCode < 300) {
        resolve({
          name: service.name,
          status: 'healthy',
          responseTime,
          statusCode: res.statusCode,
          critical: service.critical
        });
      } else {
        resolve({
          name: service.name,
          status: 'unhealthy',
          responseTime,
          statusCode: res.statusCode,
          critical: service.critical,
          error: `HTTP ${res.statusCode}`
        });
      }
    });

    req.on('error', (error) => {
      const responseTime = Date.now() - startTime;
      resolve({
        name: service.name,
        status: 'error',
        responseTime,
        critical: service.critical,
        error: error.message
      });
    });

    req.on('timeout', () => {
      req.destroy();
      const responseTime = Date.now() - startTime;
      resolve({
        name: service.name,
        status: 'timeout',
        responseTime,
        critical: service.critical,
        error: `Timeout after ${service.timeout}ms`
      });
    });
  });
}

/**
 * 格式化状态显示
 */
function formatStatus(result) {
  const statusIcons = {
    healthy: '✅',
    unhealthy: '❌',
    error: '💥',
    timeout: '⏰'
  };

  const icon = statusIcons[result.status] || '❓';
  const critical = result.critical ? '[关键]' : '[可选]';
  
  let output = `${icon} ${critical} ${result.name}`;
  
  if (result.status === 'healthy') {
    output += ` (${result.responseTime}ms)`;
  } else {
    output += ` - ${result.error}`;
  }
  
  return output;
}

/**
 * 生成健康报告
 */
function generateReport(results) {
  const healthy = results.filter(r => r.status === 'healthy').length;
  const total = results.length;
  const criticalIssues = results.filter(r => r.critical && r.status !== 'healthy').length;
  
  console.log('\n📊 系统健康报告');
  console.log('='.repeat(50));
  console.log(`总服务数: ${total}`);
  console.log(`健康服务: ${healthy}`);
  console.log(`异常服务: ${total - healthy}`);
  console.log(`关键问题: ${criticalIssues}`);
  
  if (criticalIssues === 0) {
    console.log('\n🎉 系统运行正常！');
  } else {
    console.log('\n⚠️  发现关键问题，请检查相关服务！');
  }
  
  // 性能统计
  const healthyServices = results.filter(r => r.status === 'healthy');
  if (healthyServices.length > 0) {
    const avgResponseTime = healthyServices.reduce((sum, r) => sum + r.responseTime, 0) / healthyServices.length;
    console.log(`\n📈 平均响应时间: ${Math.round(avgResponseTime)}ms`);
  }
  
  return criticalIssues === 0;
}

/**
 * 主函数
 */
async function main() {
  console.log('🔍 开始系统健康检查...\n');
  
  try {
    // 并行检查所有服务
    const results = await Promise.all(
      services.map(service => checkService(service))
    );
    
    // 显示结果
    results.forEach(result => {
      console.log(formatStatus(result));
    });
    
    // 生成报告
    const isHealthy = generateReport(results);
    
    // 退出码
    process.exit(isHealthy ? 0 : 1);
    
  } catch (error) {
    console.error('💥 健康检查失败:', error.message);
    process.exit(1);
  }
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
使用方法: node health-check.js [选项]

选项:
  -h, --help     显示帮助信息
  --json         以JSON格式输出结果
  --watch        持续监控模式
  --interval     监控间隔(秒，默认30)

示例:
  node health-check.js                # 单次检查
  node health-check.js --json         # JSON输出
  node health-check.js --watch        # 持续监控
  node health-check.js --watch --interval 60  # 60秒间隔监控
`);
  process.exit(0);
}

// JSON输出模式
if (process.argv.includes('--json')) {
  const originalLog = console.log;
  const logs = [];
  console.log = (...args) => logs.push(args.join(' '));
  
  main().then(() => {
    console.log = originalLog;
    console.log(JSON.stringify({
      timestamp: new Date().toISOString(),
      logs: logs
    }, null, 2));
  });
} else if (process.argv.includes('--watch')) {
  // 监控模式
  const intervalIndex = process.argv.indexOf('--interval');
  const interval = intervalIndex !== -1 ? parseInt(process.argv[intervalIndex + 1]) * 1000 : 30000;
  
  console.log(`🔄 启动持续监控模式 (间隔: ${interval/1000}秒)`);
  console.log('按 Ctrl+C 停止监控\n');
  
  const runCheck = async () => {
    console.log(`\n⏰ ${new Date().toLocaleString()}`);
    await main().catch(() => {});
  };
  
  // 立即执行一次
  runCheck();
  
  // 定时执行
  const timer = setInterval(runCheck, interval);
  
  // 优雅退出
  process.on('SIGINT', () => {
    console.log('\n\n👋 停止监控');
    clearInterval(timer);
    process.exit(0);
  });
} else {
  // 单次检查
  main();
}
