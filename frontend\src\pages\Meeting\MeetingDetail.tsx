import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Tag,
  Typography,
  Descriptions,
  Tabs,
  List,
  Avatar,
  Progress,
  Divider,
  Timeline,
  Statistic,
  Modal,
  message,
  Spin,
  Empty,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  DownloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SoundOutlined,
  UserOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  CheckSquareOutlined,
} from '@ant-design/icons';
import { useMeetingStore } from '@/stores/meetingStore';
import { Meeting, Speaker, TranscriptionSegment, Task } from '@/types/api';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const MeetingDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentMeeting, isLoading, fetchMeetingById, deleteMeeting } = useMeetingStore();
  
  const [audioPlaying, setAudioPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [activeTab, setActiveTab] = useState('overview');

  // 获取会议详情
  useEffect(() => {
    if (id) {
      fetchMeetingById(parseInt(id));
    }
  }, [id, fetchMeetingById]);

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!currentMeeting) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Empty description="会议不存在" />
      </div>
    );
  }

  const meeting = currentMeeting;

  // 处理删除
  const handleDelete = () => {
    Modal.confirm({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这个会议吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteMeeting(meeting.id);
          message.success('删除成功');
          navigate('/meetings');
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig = {
      TRANSCRIBING: { color: 'processing', text: '转写中' },
      PENDING: { color: 'warning', text: '待处理' },
      COMPLETED: { color: 'success', text: '已完成' },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 格式化时长
  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0:00';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // 格式化时间戳
  const formatTimestamp = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // 获取说话人颜色
  const getSpeakerColor = (index: number) => {
    const colors = ['#1890ff', '#52c41a', '#fa8c16', '#eb2f96', '#722ed1', '#13c2c2'];
    return colors[index % colors.length];
  };

  // 渲染概览标签页
  const renderOverviewTab = () => (
    <div>
      <Row gutter={[16, 16]}>
        {/* 基本信息 */}
        <Col span={24}>
          <Card title="基本信息">
            <Descriptions column={2}>
              <Descriptions.Item label="会议标题">{meeting.title}</Descriptions.Item>
              <Descriptions.Item label="会议时间">
                {dayjs(meeting.date).format('YYYY-MM-DD HH:mm')}
              </Descriptions.Item>
              <Descriptions.Item label="客户名称">
                {meeting.customerName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="会议时长">
                {formatDuration(meeting.duration)}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {getStatusTag(meeting.status)}
              </Descriptions.Item>
              <Descriptions.Item label="转写准确率">
                {meeting.transcriptionAccuracy ? `${(meeting.transcriptionAccuracy * 100).toFixed(1)}%` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="参与人员" span={2}>
                {meeting.participants && meeting.participants.length > 0 ? (
                  <Space wrap>
                    {meeting.participants.map((participant, index) => (
                      <Tag key={index} icon={<UserOutlined />}>
                        {participant}
                      </Tag>
                    ))}
                  </Space>
                ) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="会议标签" span={2}>
                {meeting.tags && meeting.tags.length > 0 ? (
                  <Space wrap>
                    {meeting.tags.map((tag, index) => (
                      <Tag key={index} color="blue">{tag}</Tag>
                    ))}
                  </Space>
                ) : '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 统计信息 */}
        <Col span={24}>
          <Row gutter={16}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="说话人数"
                  value={meeting.speakers?.length || 0}
                  prefix={<UserOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="转写片段"
                  value={meeting.transcriptionSegments?.length || 0}
                  prefix={<FileTextOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="提取任务"
                  value={meeting.tasks?.length || 0}
                  prefix={<CheckSquareOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="文件大小"
                  value={meeting.audioFileSize ? (meeting.audioFileSize / 1024 / 1024).toFixed(1) : 0}
                  suffix="MB"
                  prefix={<SoundOutlined />}
                />
              </Card>
            </Col>
          </Row>
        </Col>

        {/* 智能纪要 */}
        {meeting.summaryContent && (
          <Col span={24}>
            <Card title="智能纪要">
              <Paragraph>
                {meeting.summaryContent}
              </Paragraph>
            </Card>
          </Col>
        )}
      </Row>
    </div>
  );

  // 渲染转写标签页
  const renderTranscriptionTab = () => (
    <div>
      {meeting.transcriptionText ? (
        <Card title="转写文本">
          <div style={{ marginBottom: 16 }}>
            <Text type="secondary">
              转写准确率: {meeting.transcriptionAccuracy ? `${(meeting.transcriptionAccuracy * 100).toFixed(1)}%` : '未知'}
            </Text>
          </div>
          <div className="transcription-text">
            {meeting.transcriptionSegments && meeting.transcriptionSegments.length > 0 ? (
              meeting.transcriptionSegments.map((segment, index) => (
                <div key={index} className="transcription-segment">
                  <Space>
                    <Text className="transcription-timestamp">
                      {formatTimestamp(segment.startTime)}
                    </Text>
                    {segment.speaker && (
                      <Tag 
                        color={getSpeakerColor(meeting.speakers?.findIndex(s => s.id === segment.speakerId) || 0)}
                        size="small"
                      >
                        {segment.speaker.speakerName || segment.speaker.speakerLabel}
                      </Tag>
                    )}
                  </Space>
                  <div style={{ marginTop: 4 }}>
                    {segment.text}
                  </div>
                </div>
              ))
            ) : (
              <Paragraph>{meeting.transcriptionText}</Paragraph>
            )}
          </div>
        </Card>
      ) : (
        <Empty description="暂无转写内容" />
      )}
    </div>
  );

  // 渲染说话人标签页
  const renderSpeakersTab = () => (
    <div>
      {meeting.speakers && meeting.speakers.length > 0 ? (
        <Row gutter={16}>
          {meeting.speakers.map((speaker, index) => (
            <Col span={12} key={speaker.id}>
              <Card>
                <div style={{ textAlign: 'center', marginBottom: 16 }}>
                  <Avatar 
                    size={64} 
                    style={{ backgroundColor: getSpeakerColor(index) }}
                  >
                    {speaker.speakerName ? speaker.speakerName.charAt(0) : speaker.speakerLabel}
                  </Avatar>
                  <Title level={4} style={{ marginTop: 8 }}>
                    {speaker.speakerName || speaker.speakerLabel}
                  </Title>
                </div>
                
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="发言时长">
                    {formatDuration(speaker.totalDuration)}
                  </Descriptions.Item>
                  <Descriptions.Item label="发言次数">
                    {speaker.speechCount}
                  </Descriptions.Item>
                  <Descriptions.Item label="发言占比">
                    {speaker.speechPercentage ? `${speaker.speechPercentage.toFixed(1)}%` : '-'}
                  </Descriptions.Item>
                </Descriptions>

                {speaker.speechPercentage && (
                  <div style={{ marginTop: 16 }}>
                    <Text type="secondary">发言活跃度</Text>
                    <Progress 
                      percent={speaker.speechPercentage} 
                      strokeColor={getSpeakerColor(index)}
                      size="small"
                    />
                  </div>
                )}
              </Card>
            </Col>
          ))}
        </Row>
      ) : (
        <Empty description="暂无说话人信息" />
      )}
    </div>
  );

  // 渲染任务标签页
  const renderTasksTab = () => (
    <div>
      {meeting.tasks && meeting.tasks.length > 0 ? (
        <List
          dataSource={meeting.tasks}
          renderItem={(task) => (
            <List.Item
              actions={[
                <Button type="link">编辑</Button>,
                <Button type="link" danger>删除</Button>,
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Avatar 
                    icon={<CheckSquareOutlined />}
                    style={{ 
                      backgroundColor: task.status === 'COMPLETED' ? '#52c41a' : '#faad14' 
                    }}
                  />
                }
                title={
                  <Space>
                    <span>{task.title}</span>
                    <Tag color={task.priority === 'HIGH' ? 'red' : task.priority === 'MEDIUM' ? 'orange' : 'green'}>
                      {task.priority}
                    </Tag>
                    <Tag color={task.status === 'COMPLETED' ? 'success' : 'processing'}>
                      {task.status}
                    </Tag>
                  </Space>
                }
                description={
                  <div>
                    {task.description && <div>{task.description}</div>}
                    <Space size="large" style={{ marginTop: 8 }}>
                      {task.assigneeName && (
                        <Text type="secondary">负责人: {task.assigneeName}</Text>
                      )}
                      {task.dueDate && (
                        <Text type="secondary">
                          截止: {dayjs(task.dueDate).format('MM-DD')}
                        </Text>
                      )}
                      {task.sourceTime && (
                        <Text type="secondary">
                          来源: {formatTimestamp(task.sourceTime)}
                        </Text>
                      )}
                    </Space>
                    {task.progress > 0 && (
                      <Progress 
                        percent={task.progress} 
                        size="small" 
                        style={{ marginTop: 8, width: 200 }}
                      />
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />
      ) : (
        <Empty description="暂无任务" />
      )}
    </div>
  );

  return (
    <div>
      {/* 页面标题和操作 */}
      <div style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate('/meetings')}
              >
                返回
              </Button>
              <Title level={2} style={{ margin: 0 }}>
                {meeting.title}
              </Title>
              {getStatusTag(meeting.status)}
            </Space>
          </Col>
          <Col>
            <Space>
              {meeting.audioFilePath && (
                <Button icon={<DownloadOutlined />}>
                  下载录音
                </Button>
              )}
              <Button 
                icon={<EditOutlined />}
                onClick={() => navigate(`/meetings/${meeting.id}/edit`)}
              >
                编辑
              </Button>
              <Button 
                danger 
                icon={<DeleteOutlined />}
                onClick={handleDelete}
              >
                删除
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 音频播放器 */}
      {meeting.audioFilePath && (
        <Card style={{ marginBottom: 24 }}>
          <Row align="middle" gutter={16}>
            <Col>
              <Button
                type="primary"
                shape="circle"
                size="large"
                icon={audioPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={() => setAudioPlaying(!audioPlaying)}
              />
            </Col>
            <Col flex="auto">
              <div>
                <Text strong>会议录音</Text>
                <div style={{ marginTop: 4 }}>
                  <Text type="secondary">
                    {formatTimestamp(currentTime)} / {formatDuration(meeting.duration)}
                  </Text>
                </div>
              </div>
            </Col>
            <Col>
              <Text type="secondary">
                {meeting.audioFileSize && `${(meeting.audioFileSize / 1024 / 1024).toFixed(1)} MB`}
              </Text>
            </Col>
          </Row>
        </Card>
      )}

      {/* 详情标签页 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="概览" key="overview">
            {renderOverviewTab()}
          </TabPane>
          <TabPane tab="转写内容" key="transcription">
            {renderTranscriptionTab()}
          </TabPane>
          <TabPane tab="说话人分析" key="speakers">
            {renderSpeakersTab()}
          </TabPane>
          <TabPane tab="任务管理" key="tasks">
            {renderTasksTab()}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default MeetingDetail;
