import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { useAuthStore } from '@/stores/authStore';

// 页面组件
import Login from '@/pages/Login/Login';
import Dashboard from '@/pages/Dashboard/Dashboard';
import MeetingList from '@/pages/Meeting/MeetingList';
import MeetingDetail from '@/pages/Meeting/MeetingDetail';
import MeetingCreate from '@/pages/Meeting/MeetingCreate';
import TaskList from '@/pages/Tasks/TaskList';
import Layout from '@/components/Layout/Layout';

// 路由守卫组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuthStore();
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
};

const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuthStore();
  
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return <>{children}</>;
};

const App: React.FC = () => {
  const { checkAuth } = useAuthStore();

  // 应用启动时检查认证状态
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return (
    <ConfigProvider 
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <AntdApp>
        <Router>
          <Routes>
            {/* 公开路由 */}
            <Route 
              path="/login" 
              element={
                <PublicRoute>
                  <Login />
                </PublicRoute>
              } 
            />
            
            {/* 受保护的路由 */}
            <Route 
              path="/" 
              element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }
            >
              {/* 默认重定向到仪表板 */}
              <Route index element={<Navigate to="/dashboard" replace />} />
              
              {/* 仪表板 */}
              <Route path="dashboard" element={<Dashboard />} />
              
              {/* 会议管理 */}
              <Route path="meetings" element={<MeetingList />} />
              <Route path="meetings/create" element={<MeetingCreate />} />
              <Route path="meetings/:id" element={<MeetingDetail />} />
              
              {/* 任务管理 */}
              <Route path="tasks" element={<TaskList />} />
            </Route>
            
            {/* 404 页面 */}
            <Route 
              path="*" 
              element={
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'center', 
                  alignItems: 'center', 
                  height: '100vh',
                  flexDirection: 'column'
                }}>
                  <h1>404</h1>
                  <p>页面不存在</p>
                  <a href="/">返回首页</a>
                </div>
              } 
            />
          </Routes>
        </Router>
      </AntdApp>
    </ConfigProvider>
  );
};

export default App;
