# 🚀 智能会议系统部署检查清单

## 📋 部署前准备

### 🔧 系统要求检查
- [ ] **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- [ ] **Node.js**: 版本 18.0 或更高
- [ ] **Python**: 版本 3.9 或更高
- [ ] **内存**: 至少 8GB (推荐 16GB)
- [ ] **存储**: 至少 20GB 可用空间
- [ ] **网络**: 稳定的互联网连接 (用于下载依赖和模型)

### 📦 依赖软件安装
- [ ] **MySQL**: 8.0+ 已安装并运行
- [ ] **Redis**: 7.0+ 已安装并运行
- [ ] **Git**: 已安装 (用于克隆代码)
- [ ] **Docker**: 已安装 (可选，用于容器化部署)
- [ ] **NVIDIA驱动**: 已安装 (可选，用于GPU加速)

## 🔧 环境配置

### 📁 项目文件检查
- [ ] 项目代码已下载到本地
- [ ] `.env` 文件已创建并配置
- [ ] 所有必要的目录已创建:
  - [ ] `uploads/` - 文件上传目录
  - [ ] `logs/` - 日志目录
  - [ ] `temp/` - 临时文件目录
  - [ ] `models/` - AI模型缓存目录

### ⚙️ 配置文件检查
- [ ] **数据库配置**: `DATABASE_URL` 正确设置
- [ ] **Redis配置**: `REDIS_URL` 正确设置
- [ ] **JWT密钥**: `JWT_SECRET` 已修改为安全值
- [ ] **文件路径**: 上传和日志路径正确配置
- [ ] **AI模型**: `WHISPER_MODEL_SIZE` 根据硬件配置
- [ ] **端口配置**: 确保端口 3000, 8000, 8001 可用

## 🗄️ 数据库设置

### MySQL 数据库
- [ ] MySQL 服务正在运行
- [ ] 数据库 `meeting_db` 已创建
- [ ] 用户 `meeting_user` 已创建并授权
- [ ] 数据库连接测试成功
- [ ] Prisma 迁移已执行: `npx prisma migrate deploy`
- [ ] 数据库表结构正确创建

### Redis 缓存
- [ ] Redis 服务正在运行
- [ ] Redis 连接测试成功
- [ ] 内存配置适当 (建议至少 512MB)

## 📦 依赖安装

### 前端依赖
```bash
cd frontend
npm install
npm run build  # 生产环境
```
- [ ] 前端依赖安装成功
- [ ] 构建过程无错误
- [ ] 静态文件生成正确

### 后端依赖
```bash
cd backend
npm install
npx prisma generate
npm run build  # 生产环境
```
- [ ] 后端依赖安装成功
- [ ] Prisma 客户端生成成功
- [ ] TypeScript 编译成功

### AI服务依赖
```bash
cd ai-service
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows
pip install -r requirements.txt
```
- [ ] Python 虚拟环境创建成功
- [ ] Python 依赖安装成功
- [ ] Whisper 模型下载成功
- [ ] GPU 支持检查 (如适用)

## 🚀 服务启动

### 开发环境启动
```bash
# 方式1: 使用启动脚本
scripts/dev-start.bat  # Windows
./scripts/dev-start.sh  # Linux/macOS

# 方式2: 手动启动
npm run dev
```
- [ ] 所有服务启动成功
- [ ] 无启动错误或警告
- [ ] 端口监听正常

### 生产环境启动
```bash
# Docker 部署
docker-compose up -d

# 或手动启动
npm run start:prod
```
- [ ] 生产服务启动成功
- [ ] 容器状态健康 (如使用Docker)
- [ ] 负载均衡配置正确 (如适用)

## 🧪 功能测试

### 基础功能测试
- [ ] **前端访问**: http://localhost:3000 正常加载
- [ ] **后端API**: http://localhost:8000/health 返回正常
- [ ] **AI服务**: http://localhost:8001/health 返回正常
- [ ] **用户登录**: 使用演示账号登录成功
- [ ] **页面导航**: 所有主要页面可正常访问

### 核心功能测试
- [ ] **录音功能**: 
  - [ ] 浏览器麦克风权限获取
  - [ ] 录音开始/停止正常
  - [ ] 音频质量监控显示
  - [ ] 录音文件保存成功
- [ ] **文件上传**:
  - [ ] 音频文件上传成功
  - [ ] 文件格式验证正确
  - [ ] 文件大小限制生效
- [ ] **AI转写**:
  - [ ] 转写任务创建成功
  - [ ] 转写进度正常更新
  - [ ] 转写结果准确显示
  - [ ] 说话人识别正常 (如启用)

### 数据功能测试
- [ ] **会议管理**:
  - [ ] 创建会议成功
  - [ ] 会议列表显示正确
  - [ ] 会议详情页面正常
  - [ ] 会议编辑/删除功能
- [ ] **任务管理**:
  - [ ] 任务自动提取 (如启用)
  - [ ] 任务列表显示
  - [ ] 任务状态更新
  - [ ] 任务搜索筛选
- [ ] **搜索功能**:
  - [ ] 全文搜索正常
  - [ ] 筛选条件生效
  - [ ] 搜索结果准确

## 🔒 安全检查

### 认证和授权
- [ ] JWT 令牌生成和验证正常
- [ ] 用户权限控制正确
- [ ] API 访问控制生效
- [ ] 敏感信息不在前端暴露

### 数据安全
- [ ] 数据库连接加密
- [ ] 文件上传安全验证
- [ ] 用户输入过滤和验证
- [ ] 错误信息不泄露敏感数据

### 网络安全
- [ ] HTTPS 配置 (生产环境)
- [ ] CORS 策略正确配置
- [ ] 防火墙规则设置
- [ ] 安全头部配置

## 📊 性能检查

### 响应性能
- [ ] **前端加载**: 首页加载时间 < 3秒
- [ ] **API响应**: 平均响应时间 < 500ms
- [ ] **文件上传**: 大文件上传稳定
- [ ] **AI处理**: 转写速度符合预期

### 资源使用
- [ ] **CPU使用率**: 正常负载下 < 70%
- [ ] **内存使用**: 无内存泄漏
- [ ] **磁盘空间**: 充足的存储空间
- [ ] **网络带宽**: 满足并发需求

### 并发测试
- [ ] 多用户同时访问正常
- [ ] 并发录音/转写处理
- [ ] 数据库连接池正常
- [ ] 缓存机制生效

## 📝 日志和监控

### 日志配置
- [ ] 应用日志正常输出
- [ ] 错误日志记录完整
- [ ] 访问日志格式正确
- [ ] 日志轮转配置生效

### 监控设置
- [ ] 健康检查端点正常
- [ ] 系统监控脚本运行
- [ ] 告警机制配置
- [ ] 性能指标收集

## 🔄 备份和恢复

### 数据备份
- [ ] 数据库备份脚本测试
- [ ] 文件备份策略配置
- [ ] 备份存储位置安全
- [ ] 备份恢复流程验证

### 灾难恢复
- [ ] 恢复脚本测试
- [ ] 数据完整性验证
- [ ] 服务快速重启能力
- [ ] 故障转移机制 (如适用)

## 📚 文档和培训

### 技术文档
- [ ] 部署文档完整
- [ ] 运维手册准备
- [ ] 故障排除指南
- [ ] API 文档更新

### 用户培训
- [ ] 用户使用手册
- [ ] 功能演示准备
- [ ] 常见问题解答
- [ ] 技术支持联系方式

## ✅ 上线前最终检查

### 生产环境验证
- [ ] 所有配置项检查完毕
- [ ] 性能测试通过
- [ ] 安全扫描无高危问题
- [ ] 备份恢复测试成功

### 发布准备
- [ ] 发布计划制定
- [ ] 回滚方案准备
- [ ] 监控告警配置
- [ ] 团队通知和培训

### 上线后验证
- [ ] 服务状态监控
- [ ] 用户反馈收集
- [ ] 性能指标跟踪
- [ ] 问题快速响应机制

---

## 🆘 常见问题排查

### 服务启动失败
1. 检查端口是否被占用
2. 验证数据库连接配置
3. 确认依赖安装完整
4. 查看详细错误日志

### 录音功能异常
1. 确认浏览器支持 (Chrome/Firefox/Edge)
2. 检查麦克风权限设置
3. 使用 HTTPS 或 localhost 访问
4. 验证音频设备正常

### AI转写失败
1. 检查 Python 环境和依赖
2. 验证模型文件完整性
3. 确认系统内存充足
4. 查看 AI 服务日志

### 数据库连接问题
1. 验证 MySQL 服务状态
2. 检查连接字符串配置
3. 确认用户权限设置
4. 测试网络连通性

---

🎉 **完成所有检查项后，您的智能会议系统就可以正式投入使用了！**

如有问题，请参考技术文档或联系技术支持团队。
