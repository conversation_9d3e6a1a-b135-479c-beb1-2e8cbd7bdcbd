// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  timestamp: string;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 用户相关类型
export interface User {
  id: number;
  email: string;
  name: string;
  phone?: string;
  department?: string;
  role: 'USER' | 'ADMIN';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  remember?: boolean;
}

export interface LoginResponse {
  user: User;
  token: string;
  expiresIn: string;
}

// 会议相关类型
export interface Meeting {
  id: number;
  title: string;
  date: string;
  duration?: number;
  customerName?: string;
  participants?: string[];
  tags?: string[];
  status: 'TRANSCRIBING' | 'PENDING' | 'COMPLETED';
  audioFilePath?: string;
  audioFileSize?: number;
  transcriptionText?: string;
  transcriptionAccuracy?: number;
  summaryContent?: string;
  creatorId: number;
  creator: User;
  speakers: Speaker[];
  transcriptionSegments: TranscriptionSegment[];
  tasks: Task[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateMeetingRequest {
  title: string;
  date: string;
  customerName?: string;
  participants?: string[];
  tags?: string[];
}

export interface UpdateMeetingRequest {
  title?: string;
  date?: string;
  customerName?: string;
  participants?: string[];
  tags?: string[];
  summaryContent?: string;
}

// 说话人相关类型
export interface Speaker {
  id: number;
  meetingId: number;
  speakerLabel: string;
  speakerName?: string;
  totalDuration: number;
  speechCount: number;
  speechPercentage?: number;
  createdAt: string;
}

export interface UpdateSpeakerRequest {
  speakerName: string;
}

// 转写片段类型
export interface TranscriptionSegment {
  id: number;
  meetingId: number;
  speakerId?: number;
  speaker?: Speaker;
  startTime: number;
  endTime: number;
  text: string;
  confidence?: number;
  createdAt: string;
}

// 任务相关类型
export interface Task {
  id: number;
  meetingId: number;
  meeting: Meeting;
  title: string;
  description?: string;
  assigneeName?: string;
  assigneeId?: number;
  assignee?: User;
  dueDate?: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  progress: number;
  sourceTime?: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTaskRequest {
  meetingId: number;
  title: string;
  description?: string;
  assigneeName?: string;
  assigneeId?: number;
  dueDate?: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH';
  sourceTime?: number;
}

export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  assigneeName?: string;
  assigneeId?: number;
  dueDate?: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH';
  status?: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  progress?: number;
}

// 热词相关类型
export interface Hotword {
  id: number;
  word: string;
  type: 'SYSTEM' | 'USER';
  userId?: number;
  weight: number;
  usageCount: number;
  createdAt: string;
}

export interface CreateHotwordRequest {
  word: string;
  weight?: number;
}

// 录音相关类型
export interface RecordingConfig {
  sampleRate: number;
  channels: number;
  bitDepth: number;
  format: string;
}

export interface RecordingStatus {
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  volume: number;
  quality: 'good' | 'fair' | 'poor';
  noiseLevel: number;
}

export interface AudioUploadRequest {
  meetingId: number;
  audioFile: File;
  config?: RecordingConfig;
}

// 转写相关类型
export interface TranscriptionRequest {
  audioFilePath: string;
  language?: string;
  hotwords?: string[];
  enableSpeakerDiarization?: boolean;
}

export interface TranscriptionResponse {
  taskId: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  progress: number;
  result?: {
    text: string;
    segments: TranscriptionSegment[];
    speakers: Speaker[];
    accuracy: number;
  };
  error?: string;
}

// 搜索相关类型
export interface SearchRequest {
  query: string;
  type?: 'all' | 'meetings' | 'tasks' | 'transcriptions';
  dateRange?: {
    start: string;
    end: string;
  };
  speakerId?: number;
  customerId?: string;
  status?: string;
  page?: number;
  pageSize?: number;
}

export interface SearchResult {
  type: 'meeting' | 'task' | 'transcription';
  id: number;
  title: string;
  content: string;
  highlights: string[];
  score: number;
  metadata: Record<string, any>;
}

// WebSocket事件类型
export interface WebSocketEvent {
  type: string;
  data: any;
  timestamp: string;
}

export interface RecordingEvent extends WebSocketEvent {
  type: 'recording:status' | 'recording:quality' | 'recording:error';
  data: RecordingStatus | { error: string };
}

export interface TranscriptionEvent extends WebSocketEvent {
  type: 'transcription:progress' | 'transcription:completed' | 'transcription:error';
  data: TranscriptionResponse | { error: string };
}
