import Joi from 'joi';

// 用户注册验证
export const validateRegisterRequest = (data: any) => {
  const schema = Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': '邮箱地址不能为空'
    }),
    password: Joi.string().min(6).required().messages({
      'string.min': '密码长度不能少于6位',
      'any.required': '密码不能为空'
    }),
    name: Joi.string().min(2).max(50).required().messages({
      'string.min': '姓名长度不能少于2位',
      'string.max': '姓名长度不能超过50位',
      'any.required': '姓名不能为空'
    }),
    phone: Joi.string().pattern(/^1[3-9]\d{9}$/).optional().messages({
      'string.pattern.base': '请输入有效的手机号码'
    }),
    department: Joi.string().max(100).optional().messages({
      'string.max': '部门名称不能超过100位'
    })
  });

  return schema.validate(data);
};

// 用户登录验证
export const validateLoginRequest = (data: any) => {
  const schema = Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': '邮箱地址不能为空'
    }),
    password: Joi.string().required().messages({
      'any.required': '密码不能为空'
    }),
    remember: Joi.boolean().optional()
  });

  return schema.validate(data);
};

// 会议创建验证
export const validateCreateMeetingRequest = (data: any) => {
  const schema = Joi.object({
    title: Joi.string().min(1).max(255).required().messages({
      'string.min': '会议标题不能为空',
      'string.max': '会议标题不能超过255位',
      'any.required': '会议标题不能为空'
    }),
    date: Joi.date().required().messages({
      'any.required': '会议日期不能为空'
    }),
    customerName: Joi.string().max(255).optional().messages({
      'string.max': '客户名称不能超过255位'
    }),
    participants: Joi.array().items(Joi.string()).optional(),
    tags: Joi.array().items(Joi.string()).optional()
  });

  return schema.validate(data);
};

// 会议更新验证
export const validateUpdateMeetingRequest = (data: any) => {
  const schema = Joi.object({
    title: Joi.string().min(1).max(255).optional().messages({
      'string.min': '会议标题不能为空',
      'string.max': '会议标题不能超过255位'
    }),
    date: Joi.date().optional(),
    customerName: Joi.string().max(255).optional().messages({
      'string.max': '客户名称不能超过255位'
    }),
    participants: Joi.array().items(Joi.string()).optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    summaryContent: Joi.string().optional()
  });

  return schema.validate(data);
};

// 任务创建验证
export const validateCreateTaskRequest = (data: any) => {
  const schema = Joi.object({
    meetingId: Joi.number().integer().positive().required().messages({
      'number.base': '会议ID必须是数字',
      'number.positive': '会议ID必须是正数',
      'any.required': '会议ID不能为空'
    }),
    title: Joi.string().min(1).max(255).required().messages({
      'string.min': '任务标题不能为空',
      'string.max': '任务标题不能超过255位',
      'any.required': '任务标题不能为空'
    }),
    description: Joi.string().optional(),
    assigneeName: Joi.string().max(100).optional().messages({
      'string.max': '负责人姓名不能超过100位'
    }),
    assigneeId: Joi.number().integer().positive().optional().messages({
      'number.base': '负责人ID必须是数字',
      'number.positive': '负责人ID必须是正数'
    }),
    dueDate: Joi.date().optional(),
    priority: Joi.string().valid('LOW', 'MEDIUM', 'HIGH').optional().messages({
      'any.only': '优先级必须是 LOW、MEDIUM 或 HIGH'
    }),
    sourceTime: Joi.number().optional().messages({
      'number.base': '来源时间必须是数字'
    })
  });

  return schema.validate(data);
};

// 任务更新验证
export const validateUpdateTaskRequest = (data: any) => {
  const schema = Joi.object({
    title: Joi.string().min(1).max(255).optional().messages({
      'string.min': '任务标题不能为空',
      'string.max': '任务标题不能超过255位'
    }),
    description: Joi.string().optional(),
    assigneeName: Joi.string().max(100).optional().messages({
      'string.max': '负责人姓名不能超过100位'
    }),
    assigneeId: Joi.number().integer().positive().optional().messages({
      'number.base': '负责人ID必须是数字',
      'number.positive': '负责人ID必须是正数'
    }),
    dueDate: Joi.date().optional(),
    priority: Joi.string().valid('LOW', 'MEDIUM', 'HIGH').optional().messages({
      'any.only': '优先级必须是 LOW、MEDIUM 或 HIGH'
    }),
    status: Joi.string().valid('PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED').optional().messages({
      'any.only': '状态必须是 PENDING、IN_PROGRESS、COMPLETED 或 CANCELLED'
    }),
    progress: Joi.number().integer().min(0).max(100).optional().messages({
      'number.base': '进度必须是数字',
      'number.min': '进度不能小于0',
      'number.max': '进度不能大于100'
    })
  });

  return schema.validate(data);
};

// 热词创建验证
export const validateCreateHotwordRequest = (data: any) => {
  const schema = Joi.object({
    word: Joi.string().min(1).max(255).required().messages({
      'string.min': '热词不能为空',
      'string.max': '热词不能超过255位',
      'any.required': '热词不能为空'
    }),
    weight: Joi.number().min(0.1).max(10.0).optional().messages({
      'number.base': '权重必须是数字',
      'number.min': '权重不能小于0.1',
      'number.max': '权重不能大于10.0'
    })
  });

  return schema.validate(data);
};

// 分页参数验证
export const validatePaginationParams = (data: any) => {
  const schema = Joi.object({
    page: Joi.number().integer().min(1).optional().default(1).messages({
      'number.base': '页码必须是数字',
      'number.min': '页码不能小于1'
    }),
    pageSize: Joi.number().integer().min(1).max(100).optional().default(20).messages({
      'number.base': '每页数量必须是数字',
      'number.min': '每页数量不能小于1',
      'number.max': '每页数量不能大于100'
    })
  });

  return schema.validate(data);
};

// 搜索参数验证
export const validateSearchParams = (data: any) => {
  const schema = Joi.object({
    query: Joi.string().min(1).max(255).required().messages({
      'string.min': '搜索关键词不能为空',
      'string.max': '搜索关键词不能超过255位',
      'any.required': '搜索关键词不能为空'
    }),
    type: Joi.string().valid('all', 'meetings', 'tasks', 'transcriptions').optional().default('all').messages({
      'any.only': '搜索类型必须是 all、meetings、tasks 或 transcriptions'
    }),
    dateRange: Joi.object({
      start: Joi.date().required(),
      end: Joi.date().required()
    }).optional(),
    speakerId: Joi.number().integer().positive().optional().messages({
      'number.base': '说话人ID必须是数字',
      'number.positive': '说话人ID必须是正数'
    }),
    customerId: Joi.string().max(255).optional().messages({
      'string.max': '客户ID不能超过255位'
    }),
    status: Joi.string().optional()
  });

  return schema.validate(data);
};
