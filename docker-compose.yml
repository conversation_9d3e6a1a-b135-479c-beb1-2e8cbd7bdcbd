version: '3.8'

services:
  # 前端服务
  frontend:
    build: 
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
      - REACT_APP_AI_URL=http://localhost:8001
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - meeting-network

  # 后端服务
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=mysql://meeting_user:meeting_pass@mysql:3306/meeting_db
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - AI_SERVICE_URL=http://ai-service:8001
      - UPLOAD_DIR=/app/uploads
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./uploads:/app/uploads
    depends_on:
      - mysql
      - redis
      - ai-service
    networks:
      - meeting-network

  # AI服务
  ai-service:
    build: 
      context: ./ai-service
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - PYTHONPATH=/app
      - MODEL_CACHE_DIR=/app/models
      - AUDIO_TEMP_DIR=/app/temp
      - CUDA_VISIBLE_DEVICES=0
    volumes:
      - ./ai-service:/app
      - ./models:/app/models
      - ./temp:/app/temp
      - ./uploads:/app/uploads
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - meeting-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: meeting_db
      MYSQL_USER: meeting_user
      MYSQL_PASSWORD: meeting_pass
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - meeting-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - meeting-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./uploads:/var/www/uploads
    depends_on:
      - frontend
      - backend
    networks:
      - meeting-network

volumes:
  mysql_data:
  redis_data:

networks:
  meeting-network:
    driver: bridge
