import { create } from 'zustand';
import { Meeting, CreateMeetingRequest, UpdateMeetingRequest, PaginatedResponse } from '@/types/api';
import { apiService } from '@/services/api';

interface MeetingState {
  // 状态
  meetings: Meeting[];
  currentMeeting: Meeting | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  filters: {
    status?: string;
    customerName?: string;
    dateRange?: [string, string];
    tags?: string[];
  };

  // 动作
  fetchMeetings: (page?: number, pageSize?: number) => Promise<void>;
  fetchMeetingById: (id: number) => Promise<void>;
  createMeeting: (data: CreateMeetingRequest) => Promise<Meeting>;
  updateMeeting: (id: number, data: UpdateMeetingRequest) => Promise<void>;
  deleteMeeting: (id: number) => Promise<void>;
  uploadAudio: (meetingId: number, audioFile: File, onProgress?: (progress: number) => void) => Promise<void>;
  setFilters: (filters: Partial<MeetingState['filters']>) => void;
  clearError: () => void;
  clearCurrentMeeting: () => void;
}

export const useMeetingStore = create<MeetingState>((set, get) => ({
  // 初始状态
  meetings: [],
  currentMeeting: null,
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  },
  filters: {},

  // 获取会议列表
  fetchMeetings: async (page = 1, pageSize = 20) => {
    set({ isLoading: true, error: null });
    
    try {
      const { filters } = get();
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      // 添加筛选条件
      if (filters.status) {
        params.append('status', filters.status);
      }
      if (filters.customerName) {
        params.append('customerName', filters.customerName);
      }
      if (filters.dateRange) {
        params.append('startDate', filters.dateRange[0]);
        params.append('endDate', filters.dateRange[1]);
      }
      if (filters.tags && filters.tags.length > 0) {
        filters.tags.forEach(tag => params.append('tags', tag));
      }

      const response = await apiService.get<PaginatedResponse<Meeting>>(`/meetings?${params}`);
      
      set({
        meetings: response.items,
        pagination: {
          page: response.page,
          pageSize: response.pageSize,
          total: response.total,
          totalPages: response.totalPages,
        },
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || '获取会议列表失败',
      });
      throw error;
    }
  },

  // 获取单个会议详情
  fetchMeetingById: async (id: number) => {
    set({ isLoading: true, error: null });
    
    try {
      const meeting = await apiService.get<Meeting>(`/meetings/${id}`);
      
      set({
        currentMeeting: meeting,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || '获取会议详情失败',
      });
      throw error;
    }
  },

  // 创建会议
  createMeeting: async (data: CreateMeetingRequest) => {
    set({ isLoading: true, error: null });
    
    try {
      const meeting = await apiService.post<Meeting>('/meetings', data);
      
      // 更新会议列表
      const { meetings } = get();
      set({
        meetings: [meeting, ...meetings],
        currentMeeting: meeting,
        isLoading: false,
        error: null,
      });

      return meeting;
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || '创建会议失败',
      });
      throw error;
    }
  },

  // 更新会议
  updateMeeting: async (id: number, data: UpdateMeetingRequest) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedMeeting = await apiService.put<Meeting>(`/meetings/${id}`, data);
      
      // 更新会议列表
      const { meetings, currentMeeting } = get();
      const updatedMeetings = meetings.map(meeting => 
        meeting.id === id ? updatedMeeting : meeting
      );
      
      set({
        meetings: updatedMeetings,
        currentMeeting: currentMeeting?.id === id ? updatedMeeting : currentMeeting,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || '更新会议失败',
      });
      throw error;
    }
  },

  // 删除会议
  deleteMeeting: async (id: number) => {
    set({ isLoading: true, error: null });
    
    try {
      await apiService.delete(`/meetings/${id}`);
      
      // 从会议列表中移除
      const { meetings, currentMeeting } = get();
      const filteredMeetings = meetings.filter(meeting => meeting.id !== id);
      
      set({
        meetings: filteredMeetings,
        currentMeeting: currentMeeting?.id === id ? null : currentMeeting,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || '删除会议失败',
      });
      throw error;
    }
  },

  // 上传音频文件
  uploadAudio: async (meetingId: number, audioFile: File, onProgress?: (progress: number) => void) => {
    set({ isLoading: true, error: null });
    
    try {
      await apiService.upload(`/meetings/${meetingId}/upload`, audioFile, onProgress);
      
      // 重新获取会议详情
      const updatedMeeting = await apiService.get<Meeting>(`/meetings/${meetingId}`);
      
      // 更新会议列表和当前会议
      const { meetings, currentMeeting } = get();
      const updatedMeetings = meetings.map(meeting => 
        meeting.id === meetingId ? updatedMeeting : meeting
      );
      
      set({
        meetings: updatedMeetings,
        currentMeeting: currentMeeting?.id === meetingId ? updatedMeeting : currentMeeting,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || '上传音频文件失败',
      });
      throw error;
    }
  },

  // 设置筛选条件
  setFilters: (filters: Partial<MeetingState['filters']>) => {
    const { filters: currentFilters } = get();
    set({
      filters: { ...currentFilters, ...filters },
    });
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 清除当前会议
  clearCurrentMeeting: () => {
    set({ currentMeeting: null });
  },
}));
