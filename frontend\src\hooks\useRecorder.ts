import { useState, useRef, useCallback, useEffect } from 'react';
import { RecordingConfig, RecordingStatus } from '@/types/api';

interface UseRecorderOptions {
  onRecordingStart?: () => void;
  onRecordingStop?: (audioBlob: Blob) => void;
  onRecordingData?: (data: Float32Array) => void;
  onStatusChange?: (status: RecordingStatus) => void;
  config?: Partial<RecordingConfig>;
}

interface UseRecorderReturn {
  status: RecordingStatus;
  startRecording: () => Promise<void>;
  pauseRecording: () => void;
  resumeRecording: () => void;
  stopRecording: () => Promise<Blob | null>;
  isSupported: boolean;
  error: string | null;
}

export const useRecorder = (options: UseRecorderOptions = {}): UseRecorderReturn => {
  const {
    onRecordingStart,
    onRecordingStop,
    onRecordingData,
    onStatusChange,
    config = {},
  } = options;

  // 默认配置
  const defaultConfig: RecordingConfig = {
    sampleRate: 44100,
    channels: 1,
    bitDepth: 16,
    format: 'audio/wav',
    ...config,
  };

  // 状态管理
  const [status, setStatus] = useState<RecordingStatus>({
    isRecording: false,
    isPaused: false,
    duration: 0,
    volume: 0,
    quality: 'good',
    noiseLevel: 0,
  });

  const [error, setError] = useState<string | null>(null);

  // 引用
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const startTimeRef = useRef<number>(0);
  const animationFrameRef = useRef<number>(0);

  // 检查浏览器支持
  const isSupported = !!(
    navigator.mediaDevices &&
    navigator.mediaDevices.getUserMedia &&
    window.MediaRecorder
  );

  // 更新状态并触发回调
  const updateStatus = useCallback((newStatus: Partial<RecordingStatus>) => {
    setStatus(prev => {
      const updated = { ...prev, ...newStatus };
      onStatusChange?.(updated);
      return updated;
    });
  }, [onStatusChange]);

  // 音频分析
  const analyzeAudio = useCallback(() => {
    if (!analyserRef.current) return;

    const analyser = analyserRef.current;
    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    const floatArray = new Float32Array(bufferLength);

    analyser.getByteFrequencyData(dataArray);
    analyser.getFloatFrequencyData(floatArray);

    // 计算音量 (RMS)
    let sum = 0;
    for (let i = 0; i < bufferLength; i++) {
      sum += dataArray[i] * dataArray[i];
    }
    const rms = Math.sqrt(sum / bufferLength);
    const volume = Math.min(100, (rms / 255) * 100);

    // 计算噪音水平 (低频能量)
    const lowFreqEnd = Math.floor(bufferLength * 0.1); // 前10%的频率
    let lowFreqSum = 0;
    for (let i = 0; i < lowFreqEnd; i++) {
      lowFreqSum += dataArray[i];
    }
    const noiseLevel = (lowFreqSum / lowFreqEnd) / 255 * 100;

    // 评估音频质量
    let quality: 'good' | 'fair' | 'poor' = 'good';
    if (volume < 10 || noiseLevel > 30) {
      quality = 'poor';
    } else if (volume < 20 || noiseLevel > 15) {
      quality = 'fair';
    }

    // 更新状态
    updateStatus({
      volume,
      noiseLevel,
      quality,
      duration: status.isRecording ? Date.now() - startTimeRef.current : status.duration,
    });

    // 传递音频数据
    if (onRecordingData) {
      onRecordingData(floatArray);
    }

    // 继续分析
    if (status.isRecording && !status.isPaused) {
      animationFrameRef.current = requestAnimationFrame(analyzeAudio);
    }
  }, [status.isRecording, status.isPaused, status.duration, updateStatus, onRecordingData]);

  // 开始录音
  const startRecording = useCallback(async () => {
    if (!isSupported) {
      setError('浏览器不支持录音功能');
      return;
    }

    try {
      setError(null);

      // 请求麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: defaultConfig.sampleRate,
          channelCount: defaultConfig.channels,
        },
      });

      streamRef.current = stream;

      // 创建音频上下文用于分析
      audioContextRef.current = new AudioContext({
        sampleRate: defaultConfig.sampleRate,
      });

      const source = audioContextRef.current.createMediaStreamSource(stream);
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 2048;
      source.connect(analyserRef.current);

      // 创建MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: defaultConfig.format,
      });

      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      // 设置事件监听器
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(chunksRef.current, {
          type: defaultConfig.format,
        });
        onRecordingStop?.(audioBlob);
      };

      // 开始录音
      mediaRecorder.start(1000); // 每秒收集一次数据
      startTimeRef.current = Date.now();

      updateStatus({
        isRecording: true,
        isPaused: false,
        duration: 0,
      });

      // 开始音频分析
      analyzeAudio();

      onRecordingStart?.();
    } catch (error: any) {
      console.error('录音启动失败:', error);
      setError(error.message || '录音启动失败');
    }
  }, [isSupported, defaultConfig, updateStatus, analyzeAudio, onRecordingStart, onRecordingStop]);

  // 暂停录音
  const pauseRecording = useCallback(() => {
    if (mediaRecorderRef.current && status.isRecording && !status.isPaused) {
      mediaRecorderRef.current.pause();
      updateStatus({ isPaused: true });
      
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    }
  }, [status.isRecording, status.isPaused, updateStatus]);

  // 恢复录音
  const resumeRecording = useCallback(() => {
    if (mediaRecorderRef.current && status.isRecording && status.isPaused) {
      mediaRecorderRef.current.resume();
      updateStatus({ isPaused: false });
      
      // 重新开始音频分析
      analyzeAudio();
    }
  }, [status.isRecording, status.isPaused, updateStatus, analyzeAudio]);

  // 停止录音
  const stopRecording = useCallback(async (): Promise<Blob | null> => {
    if (!mediaRecorderRef.current || !status.isRecording) {
      return null;
    }

    return new Promise((resolve) => {
      const mediaRecorder = mediaRecorderRef.current!;
      
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(chunksRef.current, {
          type: defaultConfig.format,
        });
        
        // 清理资源
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
          streamRef.current = null;
        }
        
        if (audioContextRef.current) {
          audioContextRef.current.close();
          audioContextRef.current = null;
        }
        
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
        
        updateStatus({
          isRecording: false,
          isPaused: false,
          volume: 0,
        });
        
        resolve(audioBlob);
      };
      
      mediaRecorder.stop();
    });
  }, [status.isRecording, defaultConfig.format, updateStatus]);

  // 清理资源
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  return {
    status,
    startRecording,
    pauseRecording,
    resumeRecording,
    stopRecording,
    isSupported,
    error,
  };
};
