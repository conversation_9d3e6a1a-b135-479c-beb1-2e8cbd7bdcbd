// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id          Int      @id @default(autoincrement())
  email       String   @unique
  passwordHash String  @map("password_hash")
  name        String
  phone       String?
  department  String?
  role        Role     @default(USER)
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  meetings    Meeting[]
  tasks       Task[]
  hotwords    Hotword[]

  @@map("users")
}

model Meeting {
  id                    Int      @id @default(autoincrement())
  title                 String
  date                  DateTime
  duration              Int?     // 录音时长(秒)
  customerName          String?  @map("customer_name")
  participants          Json?    // JSON格式存储参会人员
  tags                  Json?    // 会议标签
  status                MeetingStatus @default(TRANSCRIBING)
  audioFilePath         String?  @map("audio_file_path")
  audioFileSize         BigInt?  @map("audio_file_size")
  transcriptionText     String?  @db.LongText @map("transcription_text")
  transcriptionAccuracy Decimal? @db.Decimal(5, 2) @map("transcription_accuracy")
  summaryContent        String?  @db.LongText @map("summary_content")
  creatorId             Int      @map("creator_id")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  // 关联关系
  creator               User     @relation(fields: [creatorId], references: [id])
  speakers              Speaker[]
  transcriptionSegments TranscriptionSegment[]
  tasks                 Task[]

  @@index([creatorId, date])
  @@index([status])
  @@map("meetings")
}

model Speaker {
  id                Int     @id @default(autoincrement())
  meetingId         Int     @map("meeting_id")
  speakerLabel      String  @map("speaker_label") // Speaker_A, Speaker_B等
  speakerName       String? @map("speaker_name")  // 用户标记的真实姓名
  totalDuration     Int     @default(0) @map("total_duration") // 发言总时长(秒)
  speechCount       Int     @default(0) @map("speech_count")   // 发言次数
  speechPercentage  Decimal? @db.Decimal(5, 2) @map("speech_percentage") // 发言占比
  createdAt         DateTime @default(now()) @map("created_at")

  // 关联关系
  meeting           Meeting @relation(fields: [meetingId], references: [id], onDelete: Cascade)
  transcriptionSegments TranscriptionSegment[]

  @@map("speakers")
}

model TranscriptionSegment {
  id         Int      @id @default(autoincrement())
  meetingId  Int      @map("meeting_id")
  speakerId  Int?     @map("speaker_id")
  startTime  Decimal  @db.Decimal(10, 3) @map("start_time") // 开始时间(秒)
  endTime    Decimal  @db.Decimal(10, 3) @map("end_time")   // 结束时间(秒)
  text       String   @db.Text
  confidence Decimal? @db.Decimal(5, 4) // 置信度
  createdAt  DateTime @default(now()) @map("created_at")

  // 关联关系
  meeting    Meeting  @relation(fields: [meetingId], references: [id], onDelete: Cascade)
  speaker    Speaker? @relation(fields: [speakerId], references: [id])

  @@index([meetingId, startTime])
  @@map("transcription_segments")
}

model Task {
  id          Int        @id @default(autoincrement())
  meetingId   Int        @map("meeting_id")
  title       String
  description String?    @db.Text
  assigneeName String?   @map("assignee_name")
  assigneeId  Int?       @map("assignee_id")
  dueDate     DateTime?  @map("due_date")
  priority    Priority   @default(MEDIUM)
  status      TaskStatus @default(PENDING)
  progress    Int        @default(0) // 进度百分比
  sourceTime  Decimal?   @db.Decimal(10, 3) @map("source_time") // 任务来源时间点
  createdAt   DateTime   @default(now()) @map("created_at")
  updatedAt   DateTime   @updatedAt @map("updated_at")

  // 关联关系
  meeting     Meeting    @relation(fields: [meetingId], references: [id], onDelete: Cascade)
  assignee    User?      @relation(fields: [assigneeId], references: [id])

  @@index([assigneeId, status])
  @@index([meetingId, status])
  @@map("tasks")
}

model Hotword {
  id         Int         @id @default(autoincrement())
  word       String
  type       HotwordType
  userId     Int?        @map("user_id") // 用户级热词关联的用户ID
  weight     Decimal     @default(1.0) @db.Decimal(3, 2)
  usageCount Int         @default(0) @map("usage_count")
  createdAt  DateTime    @default(now()) @map("created_at")

  // 关联关系
  user       User?       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, word])
  @@index([userId, type])
  @@map("hotwords")
}

// 枚举类型
enum Role {
  USER
  ADMIN
}

enum MeetingStatus {
  TRANSCRIBING
  PENDING
  COMPLETED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum HotwordType {
  SYSTEM
  USER
}
