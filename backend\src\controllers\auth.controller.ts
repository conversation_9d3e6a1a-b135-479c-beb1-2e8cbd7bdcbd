import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service';
import { logger } from '../utils/logger';
import { validateLoginRequest, validateRegisterRequest } from '../utils/validation';

export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  // 用户注册
  register = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { error, value } = validateRegisterRequest(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: error.details.map(detail => detail.message),
        });
      }

      const { email, password, name, phone, department } = value;

      // 检查邮箱是否已存在
      const existingUser = await this.authService.findUserByEmail(email);
      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: '邮箱已被注册',
        });
      }

      // 创建用户
      const user = await this.authService.createUser({
        email,
        password,
        name,
        phone,
        department,
      });

      // 生成token
      const token = this.authService.generateToken(user);

      logger.info(`用户注册成功: ${email}`);

      res.status(201).json({
        success: true,
        message: '注册成功',
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            phone: user.phone,
            department: user.department,
            role: user.role,
            isActive: user.isActive,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          },
          token,
          expiresIn: '7d',
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };

  // 用户登录
  login = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { error, value } = validateLoginRequest(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: error.details.map(detail => detail.message),
        });
      }

      const { email, password, remember } = value;

      // 验证用户凭据
      const user = await this.authService.validateUser(email, password);
      if (!user) {
        return res.status(401).json({
          success: false,
          message: '邮箱或密码错误',
        });
      }

      // 检查用户是否被禁用
      if (!user.isActive) {
        return res.status(403).json({
          success: false,
          message: '账户已被禁用，请联系管理员',
        });
      }

      // 生成token
      const expiresIn = remember ? '30d' : '7d';
      const token = this.authService.generateToken(user, expiresIn);

      // 记录登录日志
      logger.info(`用户登录成功: ${email}, IP: ${req.ip}`);

      res.json({
        success: true,
        message: '登录成功',
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            phone: user.phone,
            department: user.department,
            role: user.role,
            isActive: user.isActive,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          },
          token,
          expiresIn,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };

  // 用户登出
  logout = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.userId;
      
      if (userId) {
        logger.info(`用户登出: ${userId}`);
      }

      res.json({
        success: true,
        message: '登出成功',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };

  // 刷新token
  refreshToken = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '无效的token',
        });
      }

      // 获取用户信息
      const user = await this.authService.findUserById(userId);
      if (!user || !user.isActive) {
        return res.status(401).json({
          success: false,
          message: '用户不存在或已被禁用',
        });
      }

      // 生成新token
      const token = this.authService.generateToken(user);

      res.json({
        success: true,
        message: 'Token刷新成功',
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            phone: user.phone,
            department: user.department,
            role: user.role,
            isActive: user.isActive,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          },
          token,
          expiresIn: '7d',
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };

  // 获取当前用户信息
  getProfile = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未认证',
        });
      }

      const user = await this.authService.findUserById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在',
        });
      }

      res.json({
        success: true,
        message: '获取用户信息成功',
        data: {
          id: user.id,
          email: user.email,
          name: user.name,
          phone: user.phone,
          department: user.department,
          role: user.role,
          isActive: user.isActive,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };

  // 更新用户信息
  updateProfile = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未认证',
        });
      }

      const { name, phone, department } = req.body;

      const updatedUser = await this.authService.updateUser(userId, {
        name,
        phone,
        department,
      });

      logger.info(`用户更新信息: ${userId}`);

      res.json({
        success: true,
        message: '更新用户信息成功',
        data: {
          id: updatedUser.id,
          email: updatedUser.email,
          name: updatedUser.name,
          phone: updatedUser.phone,
          department: updatedUser.department,
          role: updatedUser.role,
          isActive: updatedUser.isActive,
          createdAt: updatedUser.createdAt,
          updatedAt: updatedUser.updatedAt,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };

  // 修改密码
  changePassword = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未认证',
        });
      }

      const { oldPassword, newPassword } = req.body;

      if (!oldPassword || !newPassword) {
        return res.status(400).json({
          success: false,
          message: '旧密码和新密码不能为空',
        });
      }

      if (newPassword.length < 6) {
        return res.status(400).json({
          success: false,
          message: '新密码长度不能少于6位',
        });
      }

      // 验证旧密码
      const user = await this.authService.findUserById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在',
        });
      }

      const isValidPassword = await this.authService.validatePassword(oldPassword, user.passwordHash);
      if (!isValidPassword) {
        return res.status(400).json({
          success: false,
          message: '旧密码错误',
        });
      }

      // 更新密码
      await this.authService.updatePassword(userId, newPassword);

      logger.info(`用户修改密码: ${userId}`);

      res.json({
        success: true,
        message: '密码修改成功',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  };
}
