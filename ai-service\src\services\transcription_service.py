"""
语音转写服务
使用Whisper模型进行本地语音转写
"""

import os
import asyncio
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Union
import torch
import whisper
import librosa
import soundfile as sf
from loguru import logger

from ..config.app_config import settings
from ..utils.audio_utils import AudioProcessor
from ..utils.text_utils import TextProcessor

class TranscriptionService:
    """语音转写服务"""
    
    def __init__(self):
        self.model = None
        self.device = self._get_device()
        self.audio_processor = AudioProcessor()
        self.text_processor = TextProcessor()
        self.is_initialized = False
        
        logger.info(f"初始化转写服务，设备: {self.device}")
    
    def _get_device(self) -> str:
        """获取计算设备"""
        if settings.WHISPER_DEVICE == "auto":
            return "cuda" if torch.cuda.is_available() else "cpu"
        return settings.WHISPER_DEVICE
    
    async def initialize(self) -> None:
        """初始化模型"""
        if self.is_initialized:
            return
        
        try:
            logger.info(f"加载Whisper模型: {settings.WHISPER_MODEL_SIZE}")
            
            # 在线程池中加载模型，避免阻塞事件循环
            loop = asyncio.get_event_loop()
            self.model = await loop.run_in_executor(
                None, 
                self._load_model
            )
            
            self.is_initialized = True
            logger.info("Whisper模型加载完成")
            
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            raise
    
    def _load_model(self) -> whisper.Whisper:
        """加载Whisper模型"""
        return whisper.load_model(
            settings.WHISPER_MODEL_SIZE,
            device=self.device,
            download_root=str(settings.MODEL_CACHE_DIR)
        )
    
    async def transcribe_file(
        self,
        audio_path: Union[str, Path],
        language: str = "zh",
        hotwords: Optional[List[str]] = None,
        enable_timestamps: bool = True
    ) -> Dict:
        """
        转写音频文件
        
        Args:
            audio_path: 音频文件路径
            language: 语言代码
            hotwords: 热词列表
            enable_timestamps: 是否启用时间戳
            
        Returns:
            转写结果字典
        """
        if not self.is_initialized:
            await self.initialize()
        
        try:
            audio_path = Path(audio_path)
            if not audio_path.exists():
                raise FileNotFoundError(f"音频文件不存在: {audio_path}")
            
            logger.info(f"开始转写音频文件: {audio_path}")
            
            # 预处理音频
            processed_audio_path = await self._preprocess_audio(audio_path)
            
            # 执行转写
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._transcribe_audio,
                str(processed_audio_path),
                language,
                enable_timestamps
            )
            
            # 后处理文本
            if hotwords:
                result = await self._post_process_with_hotwords(result, hotwords)
            
            # 清理临时文件
            if processed_audio_path != audio_path:
                processed_audio_path.unlink(missing_ok=True)
            
            logger.info(f"转写完成，文本长度: {len(result.get('text', ''))}")
            return result
            
        except Exception as e:
            logger.error(f"转写失败: {e}")
            raise
    
    def _transcribe_audio(
        self,
        audio_path: str,
        language: str,
        enable_timestamps: bool
    ) -> Dict:
        """执行音频转写"""
        options = {
            "language": language,
            "task": "transcribe",
            "fp16": False if self.device == "cpu" else True,
            "verbose": False,
        }
        
        if enable_timestamps:
            options["word_timestamps"] = True
        
        result = self.model.transcribe(audio_path, **options)
        
        return {
            "text": result["text"].strip(),
            "language": result["language"],
            "segments": result.get("segments", []),
            "words": self._extract_words(result.get("segments", [])) if enable_timestamps else []
        }
    
    def _extract_words(self, segments: List[Dict]) -> List[Dict]:
        """从片段中提取单词级时间戳"""
        words = []
        for segment in segments:
            if "words" in segment:
                for word in segment["words"]:
                    words.append({
                        "word": word.get("word", "").strip(),
                        "start": word.get("start", 0),
                        "end": word.get("end", 0),
                        "confidence": word.get("probability", 0)
                    })
        return words
    
    async def _preprocess_audio(self, audio_path: Path) -> Path:
        """预处理音频文件"""
        try:
            # 检查音频格式和质量
            audio_info = await self.audio_processor.get_audio_info(audio_path)
            
            # 如果音频已经是合适的格式，直接返回
            if (audio_info["sample_rate"] == settings.AUDIO_SAMPLE_RATE and
                audio_info["channels"] == settings.AUDIO_CHANNELS and
                audio_path.suffix.lower() in [".wav", ".flac"]):
                return audio_path
            
            # 需要转换格式
            logger.info(f"转换音频格式: {audio_info}")
            
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(
                suffix=".wav",
                dir=settings.AUDIO_TEMP_DIR,
                delete=False
            )
            temp_path = Path(temp_file.name)
            temp_file.close()
            
            # 转换音频
            await self.audio_processor.convert_audio(
                input_path=audio_path,
                output_path=temp_path,
                sample_rate=settings.AUDIO_SAMPLE_RATE,
                channels=settings.AUDIO_CHANNELS
            )
            
            return temp_path
            
        except Exception as e:
            logger.error(f"音频预处理失败: {e}")
            raise
    
    async def _post_process_with_hotwords(
        self,
        result: Dict,
        hotwords: List[str]
    ) -> Dict:
        """使用热词后处理转写结果"""
        try:
            # 处理主文本
            enhanced_text = await self.text_processor.enhance_with_hotwords(
                result["text"], hotwords
            )
            result["text"] = enhanced_text
            
            # 处理片段文本
            if "segments" in result:
                for segment in result["segments"]:
                    if "text" in segment:
                        segment["text"] = await self.text_processor.enhance_with_hotwords(
                            segment["text"], hotwords
                        )
            
            return result
            
        except Exception as e:
            logger.warning(f"热词后处理失败: {e}")
            return result
    
    async def transcribe_stream(
        self,
        audio_chunks: List[bytes],
        language: str = "zh",
        hotwords: Optional[List[str]] = None
    ) -> Dict:
        """
        流式转写音频数据
        
        Args:
            audio_chunks: 音频数据块列表
            language: 语言代码
            hotwords: 热词列表
            
        Returns:
            转写结果字典
        """
        try:
            # 合并音频块
            audio_data = b"".join(audio_chunks)
            
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(
                suffix=".wav",
                dir=settings.AUDIO_TEMP_DIR,
                delete=False
            )
            temp_path = Path(temp_file.name)
            
            # 写入音频数据
            with open(temp_path, "wb") as f:
                f.write(audio_data)
            
            # 转写音频
            result = await self.transcribe_file(
                audio_path=temp_path,
                language=language,
                hotwords=hotwords,
                enable_timestamps=True
            )
            
            # 清理临时文件
            temp_path.unlink(missing_ok=True)
            
            return result
            
        except Exception as e:
            logger.error(f"流式转写失败: {e}")
            raise
    
    async def get_supported_languages(self) -> List[str]:
        """获取支持的语言列表"""
        if not self.is_initialized:
            await self.initialize()
        
        # Whisper支持的语言
        return [
            "zh", "en", "ja", "ko", "es", "fr", "de", "it", "pt", "ru",
            "ar", "hi", "th", "vi", "id", "ms", "tl", "tr", "pl", "nl"
        ]
    
    async def estimate_duration(self, audio_path: Union[str, Path]) -> float:
        """估算音频时长"""
        try:
            audio_info = await self.audio_processor.get_audio_info(Path(audio_path))
            return audio_info["duration"]
        except Exception as e:
            logger.error(f"获取音频时长失败: {e}")
            return 0.0
    
    async def cleanup(self) -> None:
        """清理资源"""
        if self.model is not None:
            # Whisper模型没有显式的清理方法
            # 主要是释放GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            self.model = None
            self.is_initialized = False
            logger.info("转写服务资源已清理")
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            "model_size": settings.WHISPER_MODEL_SIZE,
            "device": self.device,
            "is_initialized": self.is_initialized,
            "gpu_available": torch.cuda.is_available(),
            "gpu_memory": torch.cuda.get_device_properties(0).total_memory if torch.cuda.is_available() else 0
        }
