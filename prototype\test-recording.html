<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PC录音功能测试原型</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .recorder-panel {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .recorder-panel.recording {
            border-color: #ff4757;
            background-color: #fff5f5;
        }
        
        .recorder-panel.paused {
            border-color: #ffa502;
            background-color: #fffbf0;
        }
        
        .status-display {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .status-recording { color: #ff4757; }
        .status-paused { color: #ffa502; }
        .status-stopped { color: #2ed573; }
        
        .time-display {
            font-size: 24px;
            font-family: 'Courier New', monospace;
            margin-bottom: 15px;
            color: #333;
        }
        
        .volume-bar {
            width: 100%;
            height: 20px;
            background-color: #eee;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .volume-fill {
            height: 100%;
            background: linear-gradient(90deg, #2ed573, #ffa502, #ff4757);
            transition: width 0.1s ease;
            border-radius: 10px;
        }
        
        .quality-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .quality-good { background-color: #2ed573; color: white; }
        .quality-fair { background-color: #ffa502; color: white; }
        .quality-poor { background-color: #ff4757; color: white; }
        
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .btn-start {
            background-color: #2ed573;
            color: white;
        }
        
        .btn-start:hover:not(:disabled) {
            background-color: #26d467;
        }
        
        .btn-pause {
            background-color: #ffa502;
            color: white;
        }
        
        .btn-pause:hover:not(:disabled) {
            background-color: #ff9500;
        }
        
        .btn-stop {
            background-color: #ff4757;
            color: white;
        }
        
        .btn-stop:hover:not(:disabled) {
            background-color: #ff3838;
        }
        
        .info-panel {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .error-message {
            background-color: #ff4757;
            color: white;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            display: none;
        }
        
        .audio-player {
            margin-top: 20px;
            text-align: center;
        }
        
        audio {
            width: 100%;
            margin-top: 10px;
        }
        
        .download-link {
            display: inline-block;
            margin-top: 10px;
            padding: 8px 16px;
            background-color: #5352ed;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .download-link:hover {
            background-color: #4742d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 PC录音功能测试原型</h1>
        
        <div class="recorder-panel" id="recorderPanel">
            <div class="status-display" id="statusDisplay">准备就绪</div>
            <div class="time-display" id="timeDisplay">00:00:00</div>
            
            <div class="volume-bar">
                <div class="volume-fill" id="volumeFill" style="width: 0%"></div>
            </div>
            
            <div class="quality-indicator quality-good" id="qualityIndicator">音质良好</div>
            
            <div class="controls">
                <button class="btn-start" id="startBtn" onclick="startRecording()">🎤 开始录音</button>
                <button class="btn-pause" id="pauseBtn" onclick="pauseRecording()" disabled>⏸️ 暂停</button>
                <button class="btn-stop" id="stopBtn" onclick="stopRecording()" disabled>⏹️ 停止</button>
            </div>
        </div>
        
        <div class="info-panel">
            <h3>📊 录音信息</h3>
            <div class="info-item">
                <span>浏览器支持:</span>
                <span id="browserSupport">检测中...</span>
            </div>
            <div class="info-item">
                <span>麦克风权限:</span>
                <span id="micPermission">未请求</span>
            </div>
            <div class="info-item">
                <span>音频格式:</span>
                <span id="audioFormat">-</span>
            </div>
            <div class="info-item">
                <span>采样率:</span>
                <span id="sampleRate">-</span>
            </div>
            <div class="info-item">
                <span>声道数:</span>
                <span id="channels">-</span>
            </div>
            <div class="info-item">
                <span>录音时长:</span>
                <span id="recordingDuration">-</span>
            </div>
            <div class="info-item">
                <span>文件大小:</span>
                <span id="fileSize">-</span>
            </div>
        </div>
        
        <div class="error-message" id="errorMessage"></div>
        
        <div class="audio-player" id="audioPlayer" style="display: none;">
            <h3>🎵 录音回放</h3>
            <audio controls id="audioPlayback"></audio>
            <br>
            <a class="download-link" id="downloadLink" download="recording.wav">📥 下载录音文件</a>
        </div>
    </div>

    <script>
        // 全局变量
        let mediaRecorder = null;
        let audioContext = null;
        let analyser = null;
        let stream = null;
        let chunks = [];
        let startTime = 0;
        let isRecording = false;
        let isPaused = false;
        let animationFrame = null;

        // DOM元素
        const recorderPanel = document.getElementById('recorderPanel');
        const statusDisplay = document.getElementById('statusDisplay');
        const timeDisplay = document.getElementById('timeDisplay');
        const volumeFill = document.getElementById('volumeFill');
        const qualityIndicator = document.getElementById('qualityIndicator');
        const startBtn = document.getElementById('startBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const stopBtn = document.getElementById('stopBtn');
        const errorMessage = document.getElementById('errorMessage');
        const audioPlayer = document.getElementById('audioPlayer');
        const audioPlayback = document.getElementById('audioPlayback');
        const downloadLink = document.getElementById('downloadLink');

        // 初始化
        window.onload = function() {
            checkBrowserSupport();
        };

        // 检查浏览器支持
        function checkBrowserSupport() {
            const browserSupport = document.getElementById('browserSupport');
            
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia && window.MediaRecorder) {
                browserSupport.textContent = '✅ 支持';
                browserSupport.style.color = '#2ed573';
            } else {
                browserSupport.textContent = '❌ 不支持';
                browserSupport.style.color = '#ff4757';
                showError('您的浏览器不支持录音功能，请使用Chrome、Firefox或Edge浏览器');
            }
        }

        // 开始录音
        async function startRecording() {
            try {
                hideError();
                
                // 请求麦克风权限
                stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 44100,
                        channelCount: 1
                    }
                });

                document.getElementById('micPermission').textContent = '✅ 已授权';
                document.getElementById('micPermission').style.color = '#2ed573';

                // 创建音频上下文
                audioContext = new AudioContext({ sampleRate: 44100 });
                const source = audioContext.createMediaStreamSource(stream);
                analyser = audioContext.createAnalyser();
                analyser.fftSize = 2048;
                source.connect(analyser);

                // 创建MediaRecorder
                const mimeType = MediaRecorder.isTypeSupported('audio/webm') ? 'audio/webm' : 'audio/mp4';
                mediaRecorder = new MediaRecorder(stream, { mimeType });
                
                document.getElementById('audioFormat').textContent = mimeType;
                document.getElementById('sampleRate').textContent = '44100 Hz';
                document.getElementById('channels').textContent = '1 (单声道)';

                chunks = [];
                
                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        chunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = () => {
                    const audioBlob = new Blob(chunks, { type: mimeType });
                    const audioUrl = URL.createObjectURL(audioBlob);
                    
                    audioPlayback.src = audioUrl;
                    downloadLink.href = audioUrl;
                    audioPlayer.style.display = 'block';
                    
                    const fileSizeMB = (audioBlob.size / 1024 / 1024).toFixed(2);
                    document.getElementById('fileSize').textContent = `${fileSizeMB} MB`;
                };

                // 开始录音
                mediaRecorder.start(1000);
                startTime = Date.now();
                isRecording = true;
                isPaused = false;

                updateUI();
                analyzeAudio();

            } catch (error) {
                console.error('录音启动失败:', error);
                document.getElementById('micPermission').textContent = '❌ 被拒绝';
                document.getElementById('micPermission').style.color = '#ff4757';
                showError('无法访问麦克风: ' + error.message);
            }
        }

        // 暂停录音
        function pauseRecording() {
            if (mediaRecorder && isRecording && !isPaused) {
                mediaRecorder.pause();
                isPaused = true;
                updateUI();
                
                if (animationFrame) {
                    cancelAnimationFrame(animationFrame);
                }
            }
        }

        // 恢复录音
        function resumeRecording() {
            if (mediaRecorder && isRecording && isPaused) {
                mediaRecorder.resume();
                isPaused = false;
                updateUI();
                analyzeAudio();
            }
        }

        // 停止录音
        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                isPaused = false;

                // 清理资源
                if (stream) {
                    stream.getTracks().forEach(track => track.stop());
                    stream = null;
                }

                if (audioContext) {
                    audioContext.close();
                    audioContext = null;
                }

                if (animationFrame) {
                    cancelAnimationFrame(animationFrame);
                }

                updateUI();
            }
        }

        // 音频分析
        function analyzeAudio() {
            if (!analyser || !isRecording || isPaused) return;

            const bufferLength = analyser.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);
            analyser.getByteFrequencyData(dataArray);

            // 计算音量 (RMS)
            let sum = 0;
            for (let i = 0; i < bufferLength; i++) {
                sum += dataArray[i] * dataArray[i];
            }
            const rms = Math.sqrt(sum / bufferLength);
            const volume = Math.min(100, (rms / 255) * 100);

            // 计算噪音水平
            const lowFreqEnd = Math.floor(bufferLength * 0.1);
            let lowFreqSum = 0;
            for (let i = 0; i < lowFreqEnd; i++) {
                lowFreqSum += dataArray[i];
            }
            const noiseLevel = (lowFreqSum / lowFreqEnd) / 255 * 100;

            // 更新音量条
            volumeFill.style.width = volume + '%';

            // 评估音质
            let quality = 'good';
            let qualityText = '音质良好';
            let qualityClass = 'quality-good';

            if (volume < 10 || noiseLevel > 30) {
                quality = 'poor';
                qualityText = '音质较差';
                qualityClass = 'quality-poor';
            } else if (volume < 20 || noiseLevel > 15) {
                quality = 'fair';
                qualityText = '音质一般';
                qualityClass = 'quality-fair';
            }

            qualityIndicator.textContent = qualityText;
            qualityIndicator.className = 'quality-indicator ' + qualityClass;

            // 继续分析
            animationFrame = requestAnimationFrame(analyzeAudio);
        }

        // 更新UI
        function updateUI() {
            if (isRecording) {
                if (isPaused) {
                    statusDisplay.textContent = '⏸️ 录音已暂停';
                    statusDisplay.className = 'status-display status-paused';
                    recorderPanel.className = 'recorder-panel paused';
                    
                    startBtn.textContent = '▶️ 继续录音';
                    startBtn.onclick = resumeRecording;
                    startBtn.disabled = false;
                    pauseBtn.disabled = true;
                    stopBtn.disabled = false;
                } else {
                    statusDisplay.textContent = '🔴 正在录音';
                    statusDisplay.className = 'status-display status-recording';
                    recorderPanel.className = 'recorder-panel recording';
                    
                    startBtn.disabled = true;
                    pauseBtn.disabled = false;
                    stopBtn.disabled = false;
                }
            } else {
                statusDisplay.textContent = '⏹️ 录音已停止';
                statusDisplay.className = 'status-display status-stopped';
                recorderPanel.className = 'recorder-panel';
                
                startBtn.textContent = '🎤 开始录音';
                startBtn.onclick = startRecording;
                startBtn.disabled = false;
                pauseBtn.disabled = true;
                stopBtn.disabled = true;
                
                volumeFill.style.width = '0%';
            }
        }

        // 更新时间显示
        function updateTimeDisplay() {
            if (isRecording && !isPaused) {
                const elapsed = Date.now() - startTime;
                const seconds = Math.floor(elapsed / 1000);
                const minutes = Math.floor(seconds / 60);
                const hours = Math.floor(minutes / 60);
                
                const timeString = 
                    String(hours).padStart(2, '0') + ':' +
                    String(minutes % 60).padStart(2, '0') + ':' +
                    String(seconds % 60).padStart(2, '0');
                
                timeDisplay.textContent = timeString;
                document.getElementById('recordingDuration').textContent = timeString;
            }
        }

        // 显示错误信息
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
        }

        // 隐藏错误信息
        function hideError() {
            errorMessage.style.display = 'none';
        }

        // 定时更新时间显示
        setInterval(updateTimeDisplay, 1000);

        // 键盘快捷键
        document.addEventListener('keydown', (event) => {
            if (event.code === 'Space' && !event.repeat) {
                event.preventDefault();
                if (!isRecording) {
                    startRecording();
                } else if (!isPaused) {
                    pauseRecording();
                } else {
                    resumeRecording();
                }
            }
        });
    </script>
</body>
</html>
