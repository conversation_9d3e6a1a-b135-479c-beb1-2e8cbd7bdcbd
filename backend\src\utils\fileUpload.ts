import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { Request, Response } from 'express';
import { config } from '../config/app.config';
import { logger } from './logger';

// 确保上传目录存在
const ensureUploadDir = () => {
  if (!fs.existsSync(config.upload.dir)) {
    fs.mkdirSync(config.upload.dir, { recursive: true });
    logger.info(`创建上传目录: ${config.upload.dir}`);
  }
};

// 文件过滤器
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // 检查文件类型
  if (config.upload.allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`不支持的文件类型: ${file.mimetype}`));
  }
};

// 存储配置
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    ensureUploadDir();
    cb(null, config.upload.dir);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const filename = `audio-${uniqueSuffix}${ext}`;
    cb(null, filename);
  },
});

// 创建multer实例
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxSize,
  },
});

// 音频文件上传中间件
export const uploadAudioMiddleware = upload.single('file');

// 处理音频文件上传
export const uploadAudioFile = (req: Request, res: Response): Promise<{
  success: boolean;
  message?: string;
  data?: {
    filePath: string;
    fileSize: number;
    originalName: string;
    mimeType: string;
  };
  error?: string;
}> => {
  return new Promise((resolve) => {
    uploadAudioMiddleware(req, res, (error) => {
      if (error) {
        logger.error(`文件上传失败: ${error.message}`);
        
        if (error instanceof multer.MulterError) {
          switch (error.code) {
            case 'LIMIT_FILE_SIZE':
              resolve({
                success: false,
                message: '文件大小超过限制',
                error: `文件大小不能超过 ${config.upload.maxSize / 1024 / 1024}MB`,
              });
              break;
            case 'LIMIT_FILE_COUNT':
              resolve({
                success: false,
                message: '文件数量超过限制',
                error: '一次只能上传一个文件',
              });
              break;
            case 'LIMIT_UNEXPECTED_FILE':
              resolve({
                success: false,
                message: '意外的文件字段',
                error: '请使用正确的文件字段名',
              });
              break;
            default:
              resolve({
                success: false,
                message: '文件上传失败',
                error: error.message,
              });
          }
        } else {
          resolve({
            success: false,
            message: '文件上传失败',
            error: error.message,
          });
        }
        return;
      }

      if (!req.file) {
        resolve({
          success: false,
          message: '没有上传文件',
          error: '请选择要上传的音频文件',
        });
        return;
      }

      const file = req.file;
      
      // 验证文件
      if (!validateAudioFile(file)) {
        // 删除已上传的文件
        fs.unlink(file.path, (unlinkError) => {
          if (unlinkError) {
            logger.error(`删除无效文件失败: ${unlinkError}`);
          }
        });
        
        resolve({
          success: false,
          message: '文件验证失败',
          error: '上传的文件不是有效的音频文件',
        });
        return;
      }

      logger.info(`文件上传成功: ${file.originalname} -> ${file.filename}`);

      resolve({
        success: true,
        message: '文件上传成功',
        data: {
          filePath: file.path,
          fileSize: file.size,
          originalName: file.originalname,
          mimeType: file.mimetype,
        },
      });
    });
  });
};

// 验证音频文件
const validateAudioFile = (file: Express.Multer.File): boolean => {
  try {
    // 检查文件大小
    if (file.size === 0) {
      logger.warn(`文件为空: ${file.originalname}`);
      return false;
    }

    // 检查文件扩展名
    const ext = path.extname(file.originalname).toLowerCase();
    const allowedExtensions = ['.wav', '.mp3', '.m4a', '.aac', '.flac', '.ogg'];
    if (!allowedExtensions.includes(ext)) {
      logger.warn(`不支持的文件扩展名: ${ext}`);
      return false;
    }

    // 检查MIME类型
    if (!config.upload.allowedTypes.includes(file.mimetype)) {
      logger.warn(`不支持的MIME类型: ${file.mimetype}`);
      return false;
    }

    // 可以添加更多验证逻辑，如文件头检查等

    return true;
  } catch (error) {
    logger.error(`文件验证失败: ${error}`);
    return false;
  }
};

// 删除文件
export const deleteFile = (filePath: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    fs.unlink(filePath, (error) => {
      if (error) {
        logger.error(`删除文件失败: ${filePath} - ${error}`);
        reject(error);
      } else {
        logger.info(`删除文件成功: ${filePath}`);
        resolve();
      }
    });
  });
};

// 获取文件信息
export const getFileInfo = (filePath: string): Promise<{
  size: number;
  mtime: Date;
  exists: boolean;
}> => {
  return new Promise((resolve) => {
    fs.stat(filePath, (error, stats) => {
      if (error) {
        resolve({
          size: 0,
          mtime: new Date(),
          exists: false,
        });
      } else {
        resolve({
          size: stats.size,
          mtime: stats.mtime,
          exists: true,
        });
      }
    });
  });
};

// 清理过期文件
export const cleanupExpiredFiles = async (maxAge: number = 7 * 24 * 60 * 60 * 1000) => {
  try {
    const files = fs.readdirSync(config.upload.dir);
    const now = Date.now();
    let deletedCount = 0;

    for (const file of files) {
      const filePath = path.join(config.upload.dir, file);
      const stats = fs.statSync(filePath);
      
      if (now - stats.mtime.getTime() > maxAge) {
        try {
          fs.unlinkSync(filePath);
          deletedCount++;
          logger.info(`清理过期文件: ${file}`);
        } catch (error) {
          logger.error(`清理文件失败: ${file} - ${error}`);
        }
      }
    }

    logger.info(`清理完成，删除了 ${deletedCount} 个过期文件`);
    return deletedCount;
  } catch (error) {
    logger.error(`清理过期文件失败: ${error}`);
    throw error;
  }
};

// 获取上传目录使用情况
export const getUploadDirUsage = (): {
  totalFiles: number;
  totalSize: number;
  totalSizeMB: number;
} => {
  try {
    const files = fs.readdirSync(config.upload.dir);
    let totalSize = 0;

    for (const file of files) {
      const filePath = path.join(config.upload.dir, file);
      const stats = fs.statSync(filePath);
      totalSize += stats.size;
    }

    return {
      totalFiles: files.length,
      totalSize,
      totalSizeMB: Math.round(totalSize / 1024 / 1024 * 100) / 100,
    };
  } catch (error) {
    logger.error(`获取上传目录使用情况失败: ${error}`);
    return {
      totalFiles: 0,
      totalSize: 0,
      totalSizeMB: 0,
    };
  }
};
