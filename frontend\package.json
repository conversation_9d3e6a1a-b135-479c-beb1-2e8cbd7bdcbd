{"name": "meeting-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "zustand": "^4.4.7", "dayjs": "^1.11.10", "react-markdown": "^9.0.1", "react-audio-visualize": "^1.0.0", "wavesurfer.js": "^7.6.0", "recordrtc": "^5.6.2", "file-saver": "^2.0.5", "@types/file-saver": "^2.0.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "@vitest/coverage-v8": "^1.0.4", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}}