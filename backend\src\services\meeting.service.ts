import { PrismaClient, Meeting, Prisma } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

export interface CreateMeetingData {
  title: string;
  date: string;
  customerName?: string;
  participants?: string[];
  tags?: string[];
  creatorId: number;
}

export interface UpdateMeetingData {
  title?: string;
  date?: string;
  customerName?: string;
  participants?: string[];
  tags?: string[];
  summaryContent?: string;
  audioFilePath?: string;
  audioFileSize?: number;
  status?: 'TRANSCRIBING' | 'PENDING' | 'COMPLETED';
  transcriptionText?: string;
  transcriptionAccuracy?: number;
  duration?: number;
}

export interface MeetingFilters {
  status?: string;
  customerName?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  tags?: string[];
}

export class MeetingService {
  // 获取会议列表
  async getMeetings(
    userId: number,
    page: number = 1,
    pageSize: number = 20,
    filters: MeetingFilters = {}
  ) {
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: Prisma.MeetingWhereInput = {
      creatorId: userId,
    };

    // 添加筛选条件
    if (filters.status) {
      where.status = filters.status as any;
    }

    if (filters.customerName) {
      where.customerName = {
        contains: filters.customerName,
        mode: 'insensitive',
      };
    }

    if (filters.dateRange) {
      where.date = {
        gte: filters.dateRange.start,
        lte: filters.dateRange.end,
      };
    }

    if (filters.tags && filters.tags.length > 0) {
      where.tags = {
        hasSome: filters.tags,
      };
    }

    // 执行查询
    const [meetings, total] = await Promise.all([
      prisma.meeting.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          speakers: true,
          tasks: {
            select: {
              id: true,
              title: true,
              status: true,
              priority: true,
              assigneeName: true,
              dueDate: true,
              progress: true,
            },
          },
          _count: {
            select: {
              transcriptionSegments: true,
            },
          },
        },
      }),
      prisma.meeting.count({ where }),
    ]);

    return {
      items: meetings,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  // 获取单个会议详情
  async getMeetingById(meetingId: number, userId: number) {
    const meeting = await prisma.meeting.findFirst({
      where: {
        id: meetingId,
        creatorId: userId,
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        speakers: {
          orderBy: { totalDuration: 'desc' },
        },
        transcriptionSegments: {
          orderBy: { startTime: 'asc' },
          include: {
            speaker: true,
          },
        },
        tasks: {
          orderBy: { createdAt: 'desc' },
          include: {
            assignee: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    return meeting;
  }

  // 创建会议
  async createMeeting(data: CreateMeetingData) {
    const meeting = await prisma.meeting.create({
      data: {
        title: data.title,
        date: new Date(data.date),
        customerName: data.customerName,
        participants: data.participants || [],
        tags: data.tags || [],
        creatorId: data.creatorId,
        status: 'PENDING',
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    logger.info(`创建会议: ${meeting.id} - ${meeting.title}`);
    return meeting;
  }

  // 更新会议
  async updateMeeting(meetingId: number, data: UpdateMeetingData) {
    const updateData: Prisma.MeetingUpdateInput = {};

    // 基本信息更新
    if (data.title !== undefined) updateData.title = data.title;
    if (data.date !== undefined) updateData.date = new Date(data.date);
    if (data.customerName !== undefined) updateData.customerName = data.customerName;
    if (data.participants !== undefined) updateData.participants = data.participants;
    if (data.tags !== undefined) updateData.tags = data.tags;
    if (data.summaryContent !== undefined) updateData.summaryContent = data.summaryContent;

    // 音频和转写相关更新
    if (data.audioFilePath !== undefined) updateData.audioFilePath = data.audioFilePath;
    if (data.audioFileSize !== undefined) updateData.audioFileSize = data.audioFileSize;
    if (data.status !== undefined) updateData.status = data.status;
    if (data.transcriptionText !== undefined) updateData.transcriptionText = data.transcriptionText;
    if (data.transcriptionAccuracy !== undefined) updateData.transcriptionAccuracy = data.transcriptionAccuracy;
    if (data.duration !== undefined) updateData.duration = data.duration;

    const meeting = await prisma.meeting.update({
      where: { id: meetingId },
      data: updateData,
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        speakers: true,
        tasks: true,
      },
    });

    logger.info(`更新会议: ${meetingId}`);
    return meeting;
  }

  // 删除会议
  async deleteMeeting(meetingId: number) {
    // 使用事务删除相关数据
    await prisma.$transaction(async (tx) => {
      // 删除转写片段
      await tx.transcriptionSegment.deleteMany({
        where: { meetingId },
      });

      // 删除说话人
      await tx.speaker.deleteMany({
        where: { meetingId },
      });

      // 删除任务
      await tx.task.deleteMany({
        where: { meetingId },
      });

      // 删除会议
      await tx.meeting.delete({
        where: { id: meetingId },
      });
    });

    logger.info(`删除会议: ${meetingId}`);
  }

  // 更新转写结果
  async updateTranscriptionResult(
    meetingId: number,
    transcriptionText: string,
    segments: any[],
    speakers: any[],
    accuracy: number,
    duration: number
  ) {
    await prisma.$transaction(async (tx) => {
      // 更新会议基本信息
      await tx.meeting.update({
        where: { id: meetingId },
        data: {
          transcriptionText,
          transcriptionAccuracy: accuracy,
          duration,
          status: 'COMPLETED',
        },
      });

      // 创建说话人记录
      const speakerMap = new Map();
      for (const speaker of speakers) {
        const createdSpeaker = await tx.speaker.create({
          data: {
            meetingId,
            speakerLabel: speaker.label,
            speakerName: speaker.name,
            totalDuration: speaker.totalDuration,
            speechCount: speaker.speechCount,
            speechPercentage: speaker.speechPercentage,
          },
        });
        speakerMap.set(speaker.label, createdSpeaker.id);
      }

      // 创建转写片段记录
      for (const segment of segments) {
        await tx.transcriptionSegment.create({
          data: {
            meetingId,
            speakerId: segment.speaker ? speakerMap.get(segment.speaker) : null,
            startTime: segment.start,
            endTime: segment.end,
            text: segment.text,
            confidence: segment.confidence,
          },
        });
      }
    });

    logger.info(`更新会议 ${meetingId} 的转写结果`);
  }

  // 搜索会议
  async searchMeetings(
    userId: number,
    query: string,
    page: number = 1,
    pageSize: number = 20
  ) {
    const skip = (page - 1) * pageSize;

    const where: Prisma.MeetingWhereInput = {
      creatorId: userId,
      OR: [
        {
          title: {
            contains: query,
            mode: 'insensitive',
          },
        },
        {
          customerName: {
            contains: query,
            mode: 'insensitive',
          },
        },
        {
          transcriptionText: {
            contains: query,
            mode: 'insensitive',
          },
        },
        {
          summaryContent: {
            contains: query,
            mode: 'insensitive',
          },
        },
      ],
    };

    const [meetings, total] = await Promise.all([
      prisma.meeting.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          speakers: true,
          tasks: true,
        },
      }),
      prisma.meeting.count({ where }),
    ]);

    return {
      items: meetings,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  // 获取会议统计
  async getMeetingStats(userId: number) {
    const stats = await prisma.meeting.aggregate({
      where: { creatorId: userId },
      _count: {
        id: true,
      },
      _sum: {
        duration: true,
      },
    });

    const statusStats = await prisma.meeting.groupBy({
      by: ['status'],
      where: { creatorId: userId },
      _count: {
        id: true,
      },
    });

    const totalTasks = await prisma.task.count({
      where: {
        meeting: {
          creatorId: userId,
        },
      },
    });

    const pendingTasks = await prisma.task.count({
      where: {
        meeting: {
          creatorId: userId,
        },
        status: 'PENDING',
      },
    });

    return {
      totalMeetings: stats._count.id,
      totalDuration: stats._sum.duration || 0,
      statusBreakdown: statusStats.reduce((acc, item) => {
        acc[item.status] = item._count.id;
        return acc;
      }, {} as Record<string, number>),
      totalTasks,
      pendingTasks,
    };
  }
}
