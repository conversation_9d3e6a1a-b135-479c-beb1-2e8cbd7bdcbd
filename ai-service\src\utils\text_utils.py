"""
文本处理工具类
"""

import re
import asyncio
from typing import List, Dict, Tuple, Optional
from difflib import SequenceMatcher
import jieba
import jieba.posseg as pseg
from loguru import logger

class TextProcessor:
    """文本处理器"""
    
    def __init__(self):
        # 初始化jieba分词
        jieba.initialize()
        
        # 常用标点符号
        self.punctuation = set('，。！？；：""''（）【】《》、')
        
        # 语气词和填充词
        self.filler_words = {
            '嗯', '啊', '呃', '额', '那个', '这个', '就是', '然后', '所以',
            '因为', '但是', '不过', '其实', '应该', '可能', '大概', '基本上'
        }
        
    async def enhance_with_hotwords(self, text: str, hotwords: List[str]) -> str:
        """
        使用热词增强文本
        
        Args:
            text: 原始文本
            hotwords: 热词列表
            
        Returns:
            增强后的文本
        """
        if not hotwords or not text:
            return text
        
        try:
            enhanced_text = text
            
            # 对每个热词进行匹配和替换
            for hotword in hotwords:
                enhanced_text = await self._replace_similar_words(enhanced_text, hotword)
            
            return enhanced_text
            
        except Exception as e:
            logger.error(f"热词增强失败: {e}")
            return text
    
    async def _replace_similar_words(self, text: str, target_word: str, threshold: float = 0.7) -> str:
        """
        替换相似的词语
        
        Args:
            text: 文本
            target_word: 目标词语
            threshold: 相似度阈值
            
        Returns:
            替换后的文本
        """
        try:
            # 分词
            words = list(jieba.cut(text))
            
            # 查找相似词并替换
            for i, word in enumerate(words):
                if len(word) > 1 and word not in self.punctuation:
                    similarity = self._calculate_similarity(word, target_word)
                    if similarity >= threshold:
                        words[i] = target_word
                        logger.debug(f"替换相似词: {word} -> {target_word} (相似度: {similarity:.3f})")
            
            return ''.join(words)
            
        except Exception as e:
            logger.error(f"相似词替换失败: {e}")
            return text
    
    def _calculate_similarity(self, word1: str, word2: str) -> float:
        """
        计算两个词的相似度
        
        Args:
            word1: 词语1
            word2: 词语2
            
        Returns:
            相似度 (0-1)
        """
        # 使用编辑距离计算相似度
        return SequenceMatcher(None, word1, word2).ratio()
    
    async def clean_text(self, text: str) -> str:
        """
        清理文本
        
        Args:
            text: 原始文本
            
        Returns:
            清理后的文本
        """
        try:
            # 移除多余的空白字符
            text = re.sub(r'\s+', ' ', text.strip())
            
            # 移除重复的标点符号
            text = re.sub(r'([，。！？；：])\1+', r'\1', text)
            
            # 移除语气词和填充词
            words = list(jieba.cut(text))
            cleaned_words = []
            
            for word in words:
                if word.strip() and word not in self.filler_words:
                    cleaned_words.append(word)
            
            return ''.join(cleaned_words)
            
        except Exception as e:
            logger.error(f"文本清理失败: {e}")
            return text
    
    async def extract_keywords(self, text: str, top_k: int = 10) -> List[Dict]:
        """
        提取关键词
        
        Args:
            text: 文本
            top_k: 返回前k个关键词
            
        Returns:
            关键词列表
        """
        try:
            # 使用TF-IDF提取关键词
            import jieba.analyse
            
            keywords = jieba.analyse.extract_tags(
                text, 
                topK=top_k, 
                withWeight=True,
                allowPOS=('n', 'nr', 'ns', 'nt', 'nz', 'v', 'vd', 'vn', 'a', 'ad')
            )
            
            return [
                {
                    'word': word,
                    'weight': weight,
                    'type': 'keyword'
                }
                for word, weight in keywords
            ]
            
        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            return []
    
    async def extract_entities(self, text: str) -> List[Dict]:
        """
        提取命名实体
        
        Args:
            text: 文本
            
        Returns:
            实体列表
        """
        try:
            entities = []
            
            # 使用词性标注识别实体
            words = pseg.cut(text)
            
            for word, flag in words:
                entity_type = None
                
                # 人名
                if flag in ('nr', 'nrf'):
                    entity_type = 'PERSON'
                # 地名
                elif flag in ('ns', 'nsf'):
                    entity_type = 'LOCATION'
                # 机构名
                elif flag in ('nt', 'ntc', 'ntcf', 'ntcb', 'ntch', 'nto', 'ntu', 'nts', 'nth'):
                    entity_type = 'ORGANIZATION'
                # 时间
                elif flag == 't':
                    entity_type = 'TIME'
                
                if entity_type and len(word) > 1:
                    entities.append({
                        'text': word,
                        'type': entity_type,
                        'confidence': 0.8  # 简单的置信度
                    })
            
            return entities
            
        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            return []
    
    async def segment_sentences(self, text: str) -> List[str]:
        """
        分句
        
        Args:
            text: 文本
            
        Returns:
            句子列表
        """
        try:
            # 使用正则表达式分句
            sentences = re.split(r'[。！？；]', text)
            
            # 清理空句子
            sentences = [s.strip() for s in sentences if s.strip()]
            
            return sentences
            
        except Exception as e:
            logger.error(f"分句失败: {e}")
            return [text]
    
    async def calculate_readability(self, text: str) -> Dict:
        """
        计算文本可读性
        
        Args:
            text: 文本
            
        Returns:
            可读性指标
        """
        try:
            # 基本统计
            char_count = len(text)
            sentences = await self.segment_sentences(text)
            sentence_count = len(sentences)
            words = list(jieba.cut(text))
            word_count = len([w for w in words if w.strip() and w not in self.punctuation])
            
            # 计算指标
            avg_sentence_length = word_count / sentence_count if sentence_count > 0 else 0
            avg_word_length = char_count / word_count if word_count > 0 else 0
            
            # 简单的可读性评分 (0-100)
            readability_score = min(100, max(0, 100 - (avg_sentence_length - 10) * 2))
            
            return {
                'char_count': char_count,
                'word_count': word_count,
                'sentence_count': sentence_count,
                'avg_sentence_length': round(avg_sentence_length, 2),
                'avg_word_length': round(avg_word_length, 2),
                'readability_score': round(readability_score, 2)
            }
            
        except Exception as e:
            logger.error(f"可读性计算失败: {e}")
            return {}
    
    async def extract_action_items(self, text: str) -> List[Dict]:
        """
        提取行动项/任务
        
        Args:
            text: 文本
            
        Returns:
            任务列表
        """
        try:
            action_items = []
            
            # 任务关键词模式
            task_patterns = [
                r'需要(.{1,50})',
                r'要求(.{1,50})',
                r'安排(.{1,50})',
                r'负责(.{1,50})',
                r'完成(.{1,50})',
                r'处理(.{1,50})',
                r'跟进(.{1,50})',
                r'确认(.{1,50})',
                r'准备(.{1,50})',
                r'提供(.{1,50})',
            ]
            
            # 时间模式
            time_patterns = [
                r'(\d+月\d+日)',
                r'(明天|后天|下周|下个月)',
                r'(\d+天内)',
                r'(本周|本月)',
            ]
            
            sentences = await self.segment_sentences(text)
            
            for sentence in sentences:
                for pattern in task_patterns:
                    matches = re.findall(pattern, sentence)
                    for match in matches:
                        # 提取时间信息
                        due_date = None
                        for time_pattern in time_patterns:
                            time_matches = re.findall(time_pattern, sentence)
                            if time_matches:
                                due_date = time_matches[0]
                                break
                        
                        # 提取负责人
                        assignee = None
                        person_words = pseg.cut(sentence)
                        for word, flag in person_words:
                            if flag == 'nr' and len(word) > 1:
                                assignee = word
                                break
                        
                        action_items.append({
                            'title': match.strip(),
                            'description': sentence.strip(),
                            'assignee': assignee,
                            'due_date': due_date,
                            'priority': 'MEDIUM',
                            'source_text': sentence
                        })
            
            return action_items
            
        except Exception as e:
            logger.error(f"任务提取失败: {e}")
            return []
    
    async def generate_summary(self, text: str, max_length: int = 200) -> str:
        """
        生成文本摘要
        
        Args:
            text: 原始文本
            max_length: 最大长度
            
        Returns:
            摘要文本
        """
        try:
            # 简单的抽取式摘要
            sentences = await self.segment_sentences(text)
            
            if len(sentences) <= 3:
                return text[:max_length]
            
            # 计算句子重要性 (基于关键词密度)
            keywords = await self.extract_keywords(text, top_k=10)
            keyword_set = {kw['word'] for kw in keywords}
            
            sentence_scores = []
            for sentence in sentences:
                score = 0
                words = list(jieba.cut(sentence))
                for word in words:
                    if word in keyword_set:
                        score += 1
                
                sentence_scores.append((sentence, score))
            
            # 选择得分最高的句子
            sentence_scores.sort(key=lambda x: x[1], reverse=True)
            
            summary_sentences = []
            current_length = 0
            
            for sentence, score in sentence_scores:
                if current_length + len(sentence) <= max_length:
                    summary_sentences.append(sentence)
                    current_length += len(sentence)
                else:
                    break
            
            return '。'.join(summary_sentences) + '。' if summary_sentences else text[:max_length]
            
        except Exception as e:
            logger.error(f"摘要生成失败: {e}")
            return text[:max_length]
