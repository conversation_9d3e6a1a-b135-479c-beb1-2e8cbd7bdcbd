# 智能会议系统技术设计文档

## 文档信息
| 项目 | 智能会议系统 |
|------|-------------|
| 版本 | V1.0 |
| 日期 | 2025-01-27 |
| 作者 | 开发团队 |

## 1. 系统架构设计

### 1.1 整体架构
```
┌─────────────────────────────────────────────────────────┐
│                    前端层 (Frontend)                     │
│  React + TypeScript + Vite + Tailwind CSS              │
└─────────────────────────────────────────────────────────┘
                            │ HTTP/WebSocket
┌─────────────────────────────────────────────────────────┐
│                    网关层 (Gateway)                      │
│  Nginx + SSL + 负载均衡 + 静态资源                       │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                   应用服务层 (Backend)                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │用户服务     │ │会议服务     │ │任务服务     │      │
│  │Node.js      │ │Node.js      │ │Node.js      │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                   AI服务层 (AI Service)                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │语音转写     │ │说话人分离   │ │纪要生成     │      │
│  │Python+Whisper│ │pyannote     │ │LLM API      │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                   数据存储层 (Storage)                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │MySQL        │ │Redis        │ │文件存储     │      │
│  │关系数据     │ │缓存/会话    │ │音频/文档    │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
└─────────────────────────────────────────────────────────┘
```

### 1.2 技术栈选择

**前端技术栈**：
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI组件**: Ant Design + Tailwind CSS
- **状态管理**: Zustand
- **路由**: React Router v6
- **HTTP客户端**: Axios
- **实时通信**: Socket.io-client
- **音频处理**: Web Audio API + MediaRecorder API

**后端技术栈**：
- **运行时**: Node.js 18+
- **框架**: Express.js + TypeScript
- **数据库ORM**: Prisma
- **身份验证**: JWT + bcrypt
- **文件上传**: Multer
- **实时通信**: Socket.io
- **任务队列**: Bull (Redis-based)
- **日志**: Winston

**AI服务技术栈**：
- **语言**: Python 3.9+
- **框架**: FastAPI
- **语音识别**: OpenAI Whisper
- **说话人分离**: pyannote-audio
- **音频处理**: librosa, pydub
- **机器学习**: PyTorch
- **HTTP客户端**: httpx

**数据存储**：
- **关系数据库**: MySQL 8.0
- **缓存**: Redis 7.0
- **文件存储**: 本地文件系统 + MinIO (可选)

**部署和运维**：
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **进程管理**: PM2
- **监控**: Prometheus + Grafana
- **日志收集**: ELK Stack (可选)

## 2. 数据库设计

### 2.1 核心表结构

```sql
-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    department VARCHAR(100),
    role ENUM('user', 'admin') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 会议表
CREATE TABLE meetings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    duration INT, -- 录音时长(秒)
    customer_name VARCHAR(255),
    participants TEXT, -- JSON格式存储参会人员
    tags JSON, -- 会议标签
    status ENUM('transcribing', 'pending', 'completed') DEFAULT 'transcribing',
    audio_file_path VARCHAR(500),
    audio_file_size BIGINT,
    transcription_text LONGTEXT,
    transcription_accuracy DECIMAL(5,2),
    summary_content LONGTEXT,
    creator_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (creator_id) REFERENCES users(id)
);

-- 说话人表
CREATE TABLE speakers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    meeting_id INT NOT NULL,
    speaker_label VARCHAR(50) NOT NULL, -- Speaker_A, Speaker_B等
    speaker_name VARCHAR(100), -- 用户标记的真实姓名
    total_duration INT DEFAULT 0, -- 发言总时长(秒)
    speech_count INT DEFAULT 0, -- 发言次数
    speech_percentage DECIMAL(5,2), -- 发言占比
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (meeting_id) REFERENCES meetings(id) ON DELETE CASCADE
);

-- 转写片段表
CREATE TABLE transcription_segments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    meeting_id INT NOT NULL,
    speaker_id INT,
    start_time DECIMAL(10,3) NOT NULL, -- 开始时间(秒)
    end_time DECIMAL(10,3) NOT NULL, -- 结束时间(秒)
    text TEXT NOT NULL,
    confidence DECIMAL(5,4), -- 置信度
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (meeting_id) REFERENCES meetings(id) ON DELETE CASCADE,
    FOREIGN KEY (speaker_id) REFERENCES speakers(id)
);

-- 任务表
CREATE TABLE tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    meeting_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    assignee_name VARCHAR(100),
    assignee_id INT,
    due_date DATE,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    progress INT DEFAULT 0, -- 进度百分比
    source_time DECIMAL(10,3), -- 任务来源时间点
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (meeting_id) REFERENCES meetings(id) ON DELETE CASCADE,
    FOREIGN KEY (assignee_id) REFERENCES users(id)
);

-- 热词表
CREATE TABLE hotwords (
    id INT PRIMARY KEY AUTO_INCREMENT,
    word VARCHAR(255) NOT NULL,
    type ENUM('system', 'user') NOT NULL,
    user_id INT, -- 用户级热词关联的用户ID
    weight DECIMAL(3,2) DEFAULT 1.0,
    usage_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_word (user_id, word)
);
```

### 2.2 索引设计

```sql
-- 性能优化索引
CREATE INDEX idx_meetings_creator_date ON meetings(creator_id, date DESC);
CREATE INDEX idx_meetings_status ON meetings(status);
CREATE INDEX idx_transcription_segments_meeting ON transcription_segments(meeting_id, start_time);
CREATE INDEX idx_tasks_assignee_status ON tasks(assignee_id, status);
CREATE INDEX idx_tasks_meeting_status ON tasks(meeting_id, status);
CREATE INDEX idx_hotwords_user_type ON hotwords(user_id, type);

-- 全文搜索索引
ALTER TABLE meetings ADD FULLTEXT(title, customer_name, transcription_text, summary_content);
ALTER TABLE transcription_segments ADD FULLTEXT(text);
```

## 3. API设计

### 3.1 RESTful API规范

**基础URL**: `https://api.meeting-system.com/v1`

**认证方式**: Bearer Token (JWT)

**响应格式**:
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2025-01-27T10:00:00Z"
}
```

### 3.2 核心API接口

**用户认证**:
```
POST /auth/login          # 用户登录
POST /auth/logout         # 用户登出
POST /auth/refresh        # 刷新token
GET  /auth/profile        # 获取用户信息
PUT  /auth/profile        # 更新用户信息
```

**会议管理**:
```
GET    /meetings          # 获取会议列表
POST   /meetings          # 创建会议
GET    /meetings/:id      # 获取会议详情
PUT    /meetings/:id      # 更新会议信息
DELETE /meetings/:id      # 删除会议
POST   /meetings/:id/upload # 上传音频文件
```

**录音功能**:
```
POST   /recording/start   # 开始录音
POST   /recording/pause   # 暂停录音
POST   /recording/resume  # 继续录音
POST   /recording/stop    # 停止录音
POST   /recording/upload  # 上传录音数据
```

**转写服务**:
```
POST   /transcription/start    # 开始转写任务
GET    /transcription/:taskId  # 获取转写状态
GET    /transcription/:taskId/result # 获取转写结果
```

### 3.3 WebSocket事件

**录音实时事件**:
```javascript
// 客户端发送
socket.emit('recording:start', { meetingId, config });
socket.emit('recording:data', { meetingId, audioData });
socket.emit('recording:stop', { meetingId });

// 服务端推送
socket.on('recording:status', { status, duration, volume });
socket.on('recording:quality', { noiseLevel, volumeLevel });
socket.on('transcription:progress', { progress, text });
```

## 4. 前端架构设计

### 4.1 项目结构
```
frontend/
├── public/
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── components/          # 通用组件
│   │   ├── Layout/
│   │   ├── AudioRecorder/
│   │   ├── AudioPlayer/
│   │   └── Common/
│   ├── pages/              # 页面组件
│   │   ├── Login/
│   │   ├── Dashboard/
│   │   ├── Meeting/
│   │   └── Tasks/
│   ├── hooks/              # 自定义Hooks
│   │   ├── useAuth.ts
│   │   ├── useRecorder.ts
│   │   └── useWebSocket.ts
│   ├── services/           # API服务
│   │   ├── api.ts
│   │   ├── auth.ts
│   │   └── meeting.ts
│   ├── stores/             # 状态管理
│   │   ├── authStore.ts
│   │   ├── meetingStore.ts
│   │   └── recordingStore.ts
│   ├── utils/              # 工具函数
│   │   ├── audio.ts
│   │   ├── format.ts
│   │   └── validation.ts
│   ├── types/              # TypeScript类型
│   │   ├── api.ts
│   │   ├── meeting.ts
│   │   └── user.ts
│   ├── App.tsx
│   └── main.tsx
├── package.json
├── vite.config.ts
└── tailwind.config.js
```

### 4.2 核心组件设计

**AudioRecorder组件**:
```typescript
interface AudioRecorderProps {
  onRecordingStart: () => void;
  onRecordingStop: (audioBlob: Blob) => void;
  onRecordingData: (data: Float32Array) => void;
  realTimeAnalysis?: boolean;
}

interface RecordingState {
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  volume: number;
  quality: 'good' | 'fair' | 'poor';
}
```

**MeetingEditor组件**:
```typescript
interface MeetingEditorProps {
  meeting: Meeting;
  transcription: TranscriptionSegment[];
  speakers: Speaker[];
  onSave: (content: string) => void;
  onExport: (format: 'pdf' | 'docx' | 'md') => void;
}
```

### 4.3 状态管理设计

**Recording Store**:
```typescript
interface RecordingStore {
  // 状态
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  audioData: Blob[];

  // 动作
  startRecording: () => Promise<void>;
  pauseRecording: () => void;
  resumeRecording: () => void;
  stopRecording: () => Promise<Blob>;
  uploadAudio: (meetingId: string, audioBlob: Blob) => Promise<void>;
}
```

## 5. 后端架构设计

### 5.1 项目结构
```
backend/
├── src/
│   ├── controllers/        # 控制器
│   │   ├── auth.controller.ts
│   │   ├── meeting.controller.ts
│   │   └── task.controller.ts
│   ├── services/           # 业务逻辑
│   │   ├── auth.service.ts
│   │   ├── meeting.service.ts
│   │   └── transcription.service.ts
│   ├── models/             # 数据模型
│   │   ├── User.ts
│   │   ├── Meeting.ts
│   │   └── Task.ts
│   ├── middleware/         # 中间件
│   │   ├── auth.middleware.ts
│   │   ├── upload.middleware.ts
│   │   └── error.middleware.ts
│   ├── routes/             # 路由
│   │   ├── auth.routes.ts
│   │   ├── meeting.routes.ts
│   │   └── api.routes.ts
│   ├── utils/              # 工具函数
│   │   ├── jwt.ts
│   │   ├── validation.ts
│   │   └── logger.ts
│   ├── config/             # 配置
│   │   ├── database.ts
│   │   ├── redis.ts
│   │   └── app.config.ts
│   ├── types/              # 类型定义
│   │   ├── api.types.ts
│   │   └── db.types.ts
│   └── app.ts
├── prisma/
│   ├── schema.prisma
│   └── migrations/
├── package.json
└── tsconfig.json
```

### 5.2 核心服务设计

**TranscriptionService**:
```typescript
class TranscriptionService {
  async startTranscription(audioFile: string, options: TranscriptionOptions): Promise<string> {
    // 1. 调用AI服务进行转写
    // 2. 处理说话人分离
    // 3. 应用热词优化
    // 4. 保存结果到数据库
  }

  async getTranscriptionStatus(taskId: string): Promise<TranscriptionStatus> {
    // 查询转写任务状态
  }

  async enhanceWithHotwords(text: string, userId: string): Promise<string> {
    // 热词优化处理
  }
}
```

**MeetingService**:
```typescript
class MeetingService {
  async createMeeting(data: CreateMeetingDto, userId: string): Promise<Meeting> {
    // 创建会议记录
  }

  async uploadAudio(meetingId: string, audioFile: Express.Multer.File): Promise<void> {
    // 1. 保存音频文件
    // 2. 启动转写任务
    // 3. 更新会议状态
  }

  async generateSummary(meetingId: string): Promise<string> {
    // 调用AI服务生成纪要
  }
}
```

## 6. AI服务架构设计

### 6.1 项目结构
```
ai-service/
├── src/
│   ├── models/             # AI模型管理
│   │   ├── whisper_model.py
│   │   ├── diarization_model.py
│   │   └── model_manager.py
│   ├── services/           # AI服务
│   │   ├── transcription_service.py
│   │   ├── speaker_service.py
│   │   └── enhancement_service.py
│   ├── api/                # API接口
│   │   ├── transcription.py
│   │   ├── speaker.py
│   │   └── health.py
│   ├── utils/              # 工具函数
│   │   ├── audio_utils.py
│   │   ├── text_utils.py
│   │   └── hotword_utils.py
│   ├── config/             # 配置
│   │   ├── model_config.py
│   │   └── app_config.py
│   └── main.py
├── models/                 # 模型文件存储
├── requirements.txt
└── Dockerfile
```

### 6.2 核心AI服务

**WhisperTranscriptionService**:
```python
class WhisperTranscriptionService:
    def __init__(self):
        self.model = whisper.load_model("large-v3")
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

    async def transcribe(self, audio_path: str, language: str = "zh") -> TranscriptionResult:
        """转写音频文件"""
        result = self.model.transcribe(
            audio_path,
            language=language,
            task="transcribe",
            fp16=False if self.device == "cpu" else True
        )
        return self._format_result(result)

    def _format_result(self, result: dict) -> TranscriptionResult:
        """格式化转写结果"""
        return TranscriptionResult(
            text=result["text"],
            segments=result["segments"],
            language=result["language"]
        )
```

**SpeakerDiarizationService**:
```python
class SpeakerDiarizationService:
    def __init__(self):
        self.pipeline = Pipeline.from_pretrained(
            "pyannote/speaker-diarization-3.1"
        )

    async def diarize(self, audio_path: str) -> DiarizationResult:
        """说话人分离"""
        diarization = self.pipeline(audio_path)
        return self._format_diarization(diarization)

    def merge_with_transcription(
        self,
        transcription: TranscriptionResult,
        diarization: DiarizationResult
    ) -> List[TranscriptionSegment]:
        """合并转写和说话人信息"""
        # 实现转写文本和说话人标签的对齐
        pass
```

## 7. 部署架构设计

### 7.1 Docker Compose配置
```yaml
version: '3.8'

services:
  # 前端服务
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend

  # 后端服务
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql://user:password@mysql:3306/meeting_db
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-secret-key
    depends_on:
      - mysql
      - redis
      - ai-service

  # AI服务
  ai-service:
    build: ./ai-service
    ports:
      - "8001:8001"
    volumes:
      - ./models:/app/models
      - ./audio_files:/app/audio_files
    environment:
      - CUDA_VISIBLE_DEVICES=0
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # 数据库
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: meeting_db
      MYSQL_USER: user
      MYSQL_PASSWORD: password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

  # 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend

volumes:
  mysql_data:
  redis_data:
```

### 7.2 生产环境配置

**Nginx配置**:
```nginx
upstream backend {
    server backend:8000;
}

upstream ai_service {
    server ai-service:8001;
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    # 前端静态文件
    location / {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # API接口
    location /api/ {
        proxy_pass http://backend/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        client_max_body_size 500M;  # 支持大文件上传
    }

    # AI服务
    location /ai/ {
        proxy_pass http://ai_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_read_timeout 300s;  # AI处理可能需要较长时间
    }

    # WebSocket支持
    location /socket.io/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

## 8. 安全设计

### 8.1 身份认证和授权
```typescript
// JWT Token结构
interface JWTPayload {
  userId: string;
  email: string;
  role: 'user' | 'admin';
  iat: number;
  exp: number;
}

// 权限中间件
const authMiddleware = (requiredRole?: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ error: 'Token required' });
    }

    try {
      const payload = jwt.verify(token, JWT_SECRET) as JWTPayload;
      req.user = payload;

      if (requiredRole && payload.role !== requiredRole) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      next();
    } catch (error) {
      return res.status(401).json({ error: 'Invalid token' });
    }
  };
};
```

### 8.2 数据安全
- **音频文件加密**: AES-256加密存储
- **敏感数据脱敏**: 转写文本中的手机号、身份证号等
- **访问控制**: 用户只能访问自己创建的会议
- **审计日志**: 记录所有关键操作

### 8.3 网络安全
- **HTTPS强制**: 所有通信使用SSL/TLS加密
- **CORS配置**: 限制跨域访问
- **Rate Limiting**: API调用频率限制
- **输入验证**: 严格的参数校验和SQL注入防护

## 9. 性能优化

### 9.1 前端优化
- **代码分割**: React.lazy + Suspense
- **资源压缩**: Gzip压缩、图片优化
- **缓存策略**: Service Worker + HTTP缓存
- **虚拟滚动**: 大列表性能优化

### 9.2 后端优化
- **数据库优化**: 索引优化、查询优化
- **缓存策略**: Redis缓存热点数据
- **连接池**: 数据库连接池管理
- **异步处理**: 转写任务异步队列

### 9.3 AI服务优化
- **模型缓存**: 预加载常用模型
- **GPU加速**: CUDA加速推理
- **批处理**: 批量处理音频文件
- **模型量化**: 减少模型大小和内存占用

## 10. 监控和日志

### 10.1 应用监控
```typescript
// 性能监控中间件
const performanceMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info('API Performance', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      userAgent: req.get('User-Agent')
    });

    // 发送到监控系统
    metrics.histogram('api_duration', duration, {
      method: req.method,
      route: req.route?.path,
      status: res.statusCode.toString()
    });
  });

  next();
};
```

### 10.2 错误处理
```typescript
// 全局错误处理
const errorHandler = (error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('Unhandled Error', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    userId: req.user?.userId
  });

  // 根据错误类型返回不同响应
  if (error instanceof ValidationError) {
    return res.status(400).json({
      success: false,
      message: '参数验证失败',
      errors: error.details
    });
  }

  if (error instanceof AuthenticationError) {
    return res.status(401).json({
      success: false,
      message: '身份验证失败'
    });
  }

  // 默认服务器错误
  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  });
};
```

### 10.3 健康检查
```typescript
// 健康检查端点
app.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    services: {
      database: await checkDatabase(),
      redis: await checkRedis(),
      aiService: await checkAIService(),
      storage: await checkStorage()
    }
  };

  const isHealthy = Object.values(health.services).every(service => service.status === 'ok');

  res.status(isHealthy ? 200 : 503).json(health);
});
```

## 11. 测试策略

### 11.1 前端测试
```typescript
// 组件测试示例
describe('AudioRecorder', () => {
  test('should start recording when start button clicked', async () => {
    const onStart = jest.fn();
    render(<AudioRecorder onRecordingStart={onStart} />);

    const startButton = screen.getByText('开始录音');
    fireEvent.click(startButton);

    expect(onStart).toHaveBeenCalled();
  });

  test('should display recording duration', async () => {
    render(<AudioRecorder />);

    // 模拟录音状态
    act(() => {
      // 触发录音开始
    });

    await waitFor(() => {
      expect(screen.getByText(/录音时长/)).toBeInTheDocument();
    });
  });
});
```

### 11.2 后端测试
```typescript
// API测试示例
describe('Meeting API', () => {
  test('POST /meetings should create new meeting', async () => {
    const meetingData = {
      title: '测试会议',
      date: '2025-01-27',
      customerName: '测试客户'
    };

    const response = await request(app)
      .post('/api/meetings')
      .set('Authorization', `Bearer ${authToken}`)
      .send(meetingData)
      .expect(201);

    expect(response.body.data).toMatchObject(meetingData);
  });
});
```

### 11.3 AI服务测试
```python
# AI服务测试示例
class TestTranscriptionService:
    def test_whisper_transcription(self):
        service = WhisperTranscriptionService()
        result = service.transcribe("test_audio.wav")

        assert result.text is not None
        assert len(result.segments) > 0
        assert result.language == "zh"

    def test_speaker_diarization(self):
        service = SpeakerDiarizationService()
        result = service.diarize("test_audio.wav")

        assert len(result.speakers) > 0
        assert all(speaker.start_time < speaker.end_time for speaker in result.speakers)
```

## 12. 部署和运维

### 12.1 CI/CD流程
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Tests
        run: |
          npm test
          python -m pytest

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build Docker Images
        run: |
          docker build -t meeting-frontend ./frontend
          docker build -t meeting-backend ./backend
          docker build -t meeting-ai ./ai-service

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Server
        run: |
          docker-compose -f docker-compose.prod.yml up -d
```

### 12.2 备份策略
- **数据库备份**: 每日自动备份，保留30天
- **音频文件备份**: 增量备份到云存储
- **配置备份**: Git版本控制
- **灾难恢复**: 异地备份和恢复流程

### 12.3 扩展策略
- **水平扩展**: 多实例负载均衡
- **数据库分片**: 按用户或时间分片
- **CDN加速**: 静态资源和音频文件CDN
- **微服务拆分**: 按业务模块拆分服务

---

## 总结

本技术设计文档详细描述了智能会议系统的完整技术架构，包括：

1. **现代化技术栈**: React + Node.js + Python + MySQL
2. **微服务架构**: 前端、后端、AI服务分离
3. **本地AI优先**: Whisper本地部署，降低成本
4. **完整的安全设计**: 身份认证、数据加密、访问控制
5. **性能优化**: 缓存、异步处理、GPU加速
6. **可扩展架构**: 容器化部署、负载均衡、监控告警

该设计确保系统具有高性能、高可用性、高安全性，同时保持良好的可维护性和扩展性。
