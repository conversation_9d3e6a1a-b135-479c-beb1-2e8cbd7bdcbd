import { apiService } from './api';
import { LoginRequest, LoginResponse, User } from '@/types/api';

export class AuthService {
  // 用户登录
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await apiService.post<LoginResponse>('/auth/login', credentials);
    
    // 保存token和用户信息到本地存储
    localStorage.setItem('auth_token', response.token);
    localStorage.setItem('user_info', JSON.stringify(response.user));
    
    return response;
  }

  // 用户登出
  async logout(): Promise<void> {
    try {
      await apiService.post('/auth/logout');
    } catch (error) {
      // 即使服务端登出失败，也要清除本地存储
      console.warn('服务端登出失败:', error);
    } finally {
      // 清除本地存储
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_info');
    }
  }

  // 刷新token
  async refreshToken(): Promise<LoginResponse> {
    const response = await apiService.post<LoginResponse>('/auth/refresh');
    
    // 更新本地存储
    localStorage.setItem('auth_token', response.token);
    localStorage.setItem('user_info', JSON.stringify(response.user));
    
    return response;
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    return apiService.get<User>('/auth/profile');
  }

  // 更新用户信息
  async updateProfile(data: Partial<User>): Promise<User> {
    const response = await apiService.put<User>('/auth/profile', data);
    
    // 更新本地存储的用户信息
    localStorage.setItem('user_info', JSON.stringify(response));
    
    return response;
  }

  // 修改密码
  async changePassword(oldPassword: string, newPassword: string): Promise<void> {
    await apiService.post('/auth/change-password', {
      oldPassword,
      newPassword,
    });
  }

  // 忘记密码
  async forgotPassword(email: string): Promise<void> {
    await apiService.post('/auth/forgot-password', { email });
  }

  // 重置密码
  async resetPassword(token: string, newPassword: string): Promise<void> {
    await apiService.post('/auth/reset-password', {
      token,
      newPassword,
    });
  }

  // 检查是否已登录
  isAuthenticated(): boolean {
    const token = localStorage.getItem('auth_token');
    return !!token;
  }

  // 获取本地存储的token
  getToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  // 获取本地存储的用户信息
  getLocalUser(): User | null {
    const userInfo = localStorage.getItem('user_info');
    if (userInfo) {
      try {
        return JSON.parse(userInfo);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        return null;
      }
    }
    return null;
  }

  // 检查用户权限
  hasRole(role: string): boolean {
    const user = this.getLocalUser();
    return user?.role === role;
  }

  // 检查是否为管理员
  isAdmin(): boolean {
    return this.hasRole('ADMIN');
  }
}

// 创建认证服务实例
export const authService = new AuthService();

// 导出默认实例
export default authService;
