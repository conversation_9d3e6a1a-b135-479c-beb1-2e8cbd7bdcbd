"""
智能会议系统 AI 服务
提供语音转写、说话人分离、文本增强等AI功能
"""

import os
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from loguru import logger

from src.config.app_config import settings
from src.services.transcription_service import TranscriptionService
from src.services.speaker_service import SpeakerService
from src.services.enhancement_service import EnhancementService
from src.api.transcription import router as transcription_router
from src.api.speaker import router as speaker_router
from src.api.health import router as health_router

# 全局服务实例
transcription_service = None
speaker_service = None
enhancement_service = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global transcription_service, speaker_service, enhancement_service
    
    logger.info("🚀 启动 AI 服务...")
    
    try:
        # 初始化服务
        logger.info("📦 初始化 AI 模型...")
        transcription_service = TranscriptionService()
        speaker_service = SpeakerService()
        enhancement_service = EnhancementService()
        
        # 预加载模型
        await transcription_service.initialize()
        await speaker_service.initialize()
        
        logger.info("✅ AI 服务启动完成")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ AI 服务启动失败: {e}")
        raise
    finally:
        logger.info("🛑 关闭 AI 服务...")
        # 清理资源
        if transcription_service:
            await transcription_service.cleanup()
        if speaker_service:
            await speaker_service.cleanup()

# 创建FastAPI应用
app = FastAPI(
    title="智能会议系统 AI 服务",
    description="提供语音转写、说话人分离、文本增强等AI功能",
    version="1.0.0",
    lifespan=lifespan
)

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(health_router, prefix="/health", tags=["健康检查"])
app.include_router(transcription_router, prefix="/transcription", tags=["语音转写"])
app.include_router(speaker_router, prefix="/speaker", tags=["说话人分离"])

# 根路径
@app.get("/")
async def root():
    """根路径信息"""
    return {
        "service": "智能会议系统 AI 服务",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "health": "/health"
    }

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "error": str(exc) if settings.DEBUG else "Internal server error"
        }
    )

# HTTP异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )

# 中间件：请求日志
@app.middleware("http")
async def log_requests(request, call_next):
    """请求日志中间件"""
    start_time = asyncio.get_event_loop().time()
    
    # 记录请求
    logger.info(f"📥 {request.method} {request.url}")
    
    # 处理请求
    response = await call_next(request)
    
    # 记录响应
    process_time = asyncio.get_event_loop().time() - start_time
    logger.info(f"📤 {request.method} {request.url} - {response.status_code} - {process_time:.3f}s")
    
    return response

# 获取服务实例的辅助函数
def get_transcription_service() -> TranscriptionService:
    """获取转写服务实例"""
    if transcription_service is None:
        raise HTTPException(status_code=503, detail="转写服务未初始化")
    return transcription_service

def get_speaker_service() -> SpeakerService:
    """获取说话人服务实例"""
    if speaker_service is None:
        raise HTTPException(status_code=503, detail="说话人服务未初始化")
    return speaker_service

def get_enhancement_service() -> EnhancementService:
    """获取文本增强服务实例"""
    if enhancement_service is None:
        raise HTTPException(status_code=503, detail="文本增强服务未初始化")
    return enhancement_service

# 导出服务获取函数供路由使用
app.state.get_transcription_service = get_transcription_service
app.state.get_speaker_service = get_speaker_service
app.state.get_enhancement_service = get_enhancement_service

if __name__ == "__main__":
    # 配置日志
    logger.add(
        "logs/ai_service.log",
        rotation="1 day",
        retention="30 days",
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    )
    
    # 启动服务
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
