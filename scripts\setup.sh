#!/bin/bash

# 智能会议系统快速安装脚本
# 使用方法: chmod +x scripts/setup.sh && ./scripts/setup.sh

set -e

echo "🚀 智能会议系统安装脚本"
echo "=========================="

# 检查系统要求
check_requirements() {
    echo "📋 检查系统要求..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        echo "❌ Node.js 版本过低，需要 18+，当前版本: $(node -v)"
        exit 1
    fi
    echo "✅ Node.js $(node -v)"
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3 未安装，请先安装 Python 3.9+"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    echo "✅ Python $python_version"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo "⚠️  Docker 未安装，将使用手动安装模式"
        USE_DOCKER=false
    else
        echo "✅ Docker $(docker --version)"
        USE_DOCKER=true
    fi
    
    # 检查GPU
    if command -v nvidia-smi &> /dev/null; then
        echo "✅ NVIDIA GPU 可用"
        GPU_AVAILABLE=true
    else
        echo "⚠️  未检测到NVIDIA GPU，将使用CPU模式"
        GPU_AVAILABLE=false
    fi
}

# 创建环境配置
setup_env() {
    echo "⚙️  配置环境变量..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        echo "✅ 已创建 .env 文件"
        
        # 生成随机JWT密钥
        jwt_secret=$(openssl rand -base64 32 2>/dev/null || echo "your-super-secret-jwt-key-$(date +%s)")
        sed -i.bak "s/your-super-secret-jwt-key-change-in-production/$jwt_secret/g" .env
        
        echo "🔑 已生成JWT密钥"
    else
        echo "✅ .env 文件已存在"
    fi
}

# Docker安装模式
install_with_docker() {
    echo "🐳 使用Docker安装..."
    
    # 检查docker-compose
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ docker-compose 未安装"
        exit 1
    fi
    
    # 构建镜像
    echo "📦 构建Docker镜像..."
    docker-compose build
    
    # 启动服务
    echo "🚀 启动服务..."
    docker-compose up -d mysql redis
    
    # 等待数据库启动
    echo "⏳ 等待数据库启动..."
    sleep 10
    
    # 数据库迁移
    echo "🗄️  执行数据库迁移..."
    docker-compose run --rm backend npm run db:migrate
    
    # 启动所有服务
    docker-compose up -d
    
    echo "✅ Docker安装完成！"
}

# 手动安装模式
install_manually() {
    echo "🔧 手动安装模式..."
    
    # 安装前端依赖
    echo "📦 安装前端依赖..."
    cd frontend
    npm install
    cd ..
    
    # 安装后端依赖
    echo "📦 安装后端依赖..."
    cd backend
    npm install
    
    # 生成Prisma客户端
    echo "🗄️  生成数据库客户端..."
    npx prisma generate
    cd ..
    
    # 安装AI服务依赖
    echo "🤖 安装AI服务依赖..."
    cd ai-service
    
    # 创建虚拟环境
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        echo "✅ 已创建Python虚拟环境"
    fi
    
    # 激活虚拟环境并安装依赖
    source venv/bin/activate
    pip install -r requirements.txt
    
    # 预下载Whisper模型
    echo "📥 下载Whisper模型..."
    python3 -c "import whisper; whisper.load_model('base')"
    
    cd ..
    
    echo "✅ 手动安装完成！"
}

# 启动服务
start_services() {
    if [ "$USE_DOCKER" = true ]; then
        echo "🚀 启动Docker服务..."
        docker-compose up -d
        
        echo "📊 服务状态:"
        docker-compose ps
    else
        echo "🚀 启动开发服务..."
        echo "请在不同终端中运行以下命令:"
        echo "1. 启动数据库: mysql 和 redis"
        echo "2. 后端服务: cd backend && npm run dev"
        echo "3. AI服务: cd ai-service && source venv/bin/activate && python main.py"
        echo "4. 前端服务: cd frontend && npm run dev"
    fi
}

# 显示访问信息
show_access_info() {
    echo ""
    echo "🎉 安装完成！"
    echo "==============="
    echo "📱 前端应用: http://localhost:3000"
    echo "🔧 后端API: http://localhost:8000"
    echo "🤖 AI服务: http://localhost:8001"
    echo "🗄️  数据库管理: npm run db:studio"
    echo ""
    echo "📚 更多信息请查看 README.md"
    echo ""
    
    if [ "$USE_DOCKER" = true ]; then
        echo "🐳 Docker命令:"
        echo "  查看日志: docker-compose logs -f"
        echo "  停止服务: docker-compose down"
        echo "  重启服务: docker-compose restart"
    fi
}

# 主函数
main() {
    check_requirements
    setup_env
    
    if [ "$USE_DOCKER" = true ]; then
        install_with_docker
    else
        install_manually
    fi
    
    start_services
    show_access_info
}

# 运行主函数
main "$@"
