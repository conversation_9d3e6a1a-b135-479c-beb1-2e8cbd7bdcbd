"""
音频处理工具类
"""

import os
import asyncio
from pathlib import Path
from typing import Dict, Union, Optional
import librosa
import soundfile as sf
import numpy as np
from loguru import logger

from ..config.app_config import settings

class AudioProcessor:
    """音频处理器"""
    
    def __init__(self):
        self.supported_formats = settings.SUPPORTED_AUDIO_EXTENSIONS
        
    async def get_audio_info(self, audio_path: Path) -> Dict:
        """
        获取音频文件信息
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            音频信息字典
        """
        try:
            # 使用soundfile获取基本信息
            info = sf.info(str(audio_path))
            
            return {
                "duration": info.frames / info.samplerate,
                "sample_rate": info.samplerate,
                "channels": info.channels,
                "frames": info.frames,
                "format": info.format,
                "subtype": info.subtype,
                "file_size": audio_path.stat().st_size,
            }
            
        except Exception as e:
            logger.error(f"获取音频信息失败: {e}")
            raise ValueError(f"无法读取音频文件: {audio_path}")
    
    async def convert_audio(
        self,
        input_path: Path,
        output_path: Path,
        sample_rate: int = None,
        channels: int = None,
        format: str = "wav"
    ) -> None:
        """
        转换音频格式
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            sample_rate: 目标采样率
            channels: 目标声道数
            format: 目标格式
        """
        try:
            # 使用默认配置
            target_sr = sample_rate or settings.AUDIO_SAMPLE_RATE
            target_channels = channels or settings.AUDIO_CHANNELS
            
            logger.info(f"转换音频: {input_path} -> {output_path}")
            logger.info(f"目标参数: {target_sr}Hz, {target_channels}声道")
            
            # 在线程池中执行音频转换
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                self._convert_audio_sync,
                str(input_path),
                str(output_path),
                target_sr,
                target_channels
            )
            
            logger.info(f"音频转换完成: {output_path}")
            
        except Exception as e:
            logger.error(f"音频转换失败: {e}")
            raise
    
    def _convert_audio_sync(
        self,
        input_path: str,
        output_path: str,
        sample_rate: int,
        channels: int
    ) -> None:
        """同步音频转换"""
        # 加载音频
        y, sr = librosa.load(input_path, sr=sample_rate, mono=(channels == 1))
        
        # 如果需要转换为立体声
        if channels == 2 and y.ndim == 1:
            y = np.stack([y, y])
        
        # 保存音频
        sf.write(output_path, y.T if y.ndim == 2 else y, sample_rate)
    
    async def validate_audio_file(self, audio_path: Path) -> bool:
        """
        验证音频文件
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            是否有效
        """
        try:
            # 检查文件是否存在
            if not audio_path.exists():
                logger.error(f"音频文件不存在: {audio_path}")
                return False
            
            # 检查文件扩展名
            if audio_path.suffix.lower() not in self.supported_formats:
                logger.error(f"不支持的音频格式: {audio_path.suffix}")
                return False
            
            # 检查文件大小
            file_size = audio_path.stat().st_size
            if file_size == 0:
                logger.error(f"音频文件为空: {audio_path}")
                return False
            
            # 检查音频信息
            info = await self.get_audio_info(audio_path)
            
            # 检查时长
            if info["duration"] > settings.MAX_AUDIO_DURATION:
                logger.error(f"音频文件过长: {info['duration']}秒 > {settings.MAX_AUDIO_DURATION}秒")
                return False
            
            if info["duration"] < 1:
                logger.error(f"音频文件过短: {info['duration']}秒")
                return False
            
            logger.info(f"音频文件验证通过: {audio_path}")
            return True
            
        except Exception as e:
            logger.error(f"音频文件验证失败: {e}")
            return False
    
    async def extract_audio_features(self, audio_path: Path) -> Dict:
        """
        提取音频特征
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            音频特征字典
        """
        try:
            loop = asyncio.get_event_loop()
            features = await loop.run_in_executor(
                None,
                self._extract_features_sync,
                str(audio_path)
            )
            
            return features
            
        except Exception as e:
            logger.error(f"音频特征提取失败: {e}")
            raise
    
    def _extract_features_sync(self, audio_path: str) -> Dict:
        """同步提取音频特征"""
        # 加载音频
        y, sr = librosa.load(audio_path, sr=settings.AUDIO_SAMPLE_RATE)
        
        # 提取特征
        features = {}
        
        # 基础特征
        features["duration"] = len(y) / sr
        features["sample_rate"] = sr
        features["rms_energy"] = float(np.sqrt(np.mean(y**2)))
        
        # 频谱特征
        spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
        features["spectral_centroid_mean"] = float(np.mean(spectral_centroids))
        features["spectral_centroid_std"] = float(np.std(spectral_centroids))
        
        # 零交叉率
        zcr = librosa.feature.zero_crossing_rate(y)[0]
        features["zero_crossing_rate_mean"] = float(np.mean(zcr))
        
        # MFCC特征
        mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
        features["mfcc_mean"] = mfccs.mean(axis=1).tolist()
        features["mfcc_std"] = mfccs.std(axis=1).tolist()
        
        # 音调特征
        pitches, magnitudes = librosa.piptrack(y=y, sr=sr)
        pitch_values = []
        for t in range(pitches.shape[1]):
            index = magnitudes[:, t].argmax()
            pitch = pitches[index, t]
            if pitch > 0:
                pitch_values.append(pitch)
        
        if pitch_values:
            features["pitch_mean"] = float(np.mean(pitch_values))
            features["pitch_std"] = float(np.std(pitch_values))
        else:
            features["pitch_mean"] = 0.0
            features["pitch_std"] = 0.0
        
        return features
    
    async def detect_silence(
        self,
        audio_path: Path,
        threshold: float = 0.01,
        min_duration: float = 0.5
    ) -> list:
        """
        检测静音片段
        
        Args:
            audio_path: 音频文件路径
            threshold: 静音阈值
            min_duration: 最小静音时长
            
        Returns:
            静音片段列表 [(start, end), ...]
        """
        try:
            loop = asyncio.get_event_loop()
            silence_segments = await loop.run_in_executor(
                None,
                self._detect_silence_sync,
                str(audio_path),
                threshold,
                min_duration
            )
            
            return silence_segments
            
        except Exception as e:
            logger.error(f"静音检测失败: {e}")
            raise
    
    def _detect_silence_sync(
        self,
        audio_path: str,
        threshold: float,
        min_duration: float
    ) -> list:
        """同步检测静音"""
        # 加载音频
        y, sr = librosa.load(audio_path, sr=settings.AUDIO_SAMPLE_RATE)
        
        # 计算RMS能量
        frame_length = int(0.025 * sr)  # 25ms帧
        hop_length = int(0.01 * sr)     # 10ms跳跃
        
        rms = librosa.feature.rms(
            y=y,
            frame_length=frame_length,
            hop_length=hop_length
        )[0]
        
        # 检测静音帧
        silence_frames = rms < threshold
        
        # 转换为时间
        times = librosa.frames_to_time(
            np.arange(len(silence_frames)),
            sr=sr,
            hop_length=hop_length
        )
        
        # 合并连续的静音片段
        silence_segments = []
        start_time = None
        
        for i, is_silence in enumerate(silence_frames):
            if is_silence and start_time is None:
                start_time = times[i]
            elif not is_silence and start_time is not None:
                duration = times[i] - start_time
                if duration >= min_duration:
                    silence_segments.append((start_time, times[i]))
                start_time = None
        
        # 处理最后一个片段
        if start_time is not None:
            duration = times[-1] - start_time
            if duration >= min_duration:
                silence_segments.append((start_time, times[-1]))
        
        return silence_segments
    
    async def normalize_audio(
        self,
        input_path: Path,
        output_path: Path,
        target_lufs: float = -23.0
    ) -> None:
        """
        音频响度标准化
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            target_lufs: 目标响度 (LUFS)
        """
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                self._normalize_audio_sync,
                str(input_path),
                str(output_path),
                target_lufs
            )
            
            logger.info(f"音频标准化完成: {output_path}")
            
        except Exception as e:
            logger.error(f"音频标准化失败: {e}")
            raise
    
    def _normalize_audio_sync(
        self,
        input_path: str,
        output_path: str,
        target_lufs: float
    ) -> None:
        """同步音频标准化"""
        # 加载音频
        y, sr = librosa.load(input_path, sr=None)
        
        # 简单的峰值标准化（实际项目中可以使用更复杂的响度标准化）
        peak = np.max(np.abs(y))
        if peak > 0:
            # 标准化到 -3dB 峰值
            target_peak = 10 ** (-3 / 20)
            y = y * (target_peak / peak)
        
        # 保存音频
        sf.write(output_path, y, sr)
