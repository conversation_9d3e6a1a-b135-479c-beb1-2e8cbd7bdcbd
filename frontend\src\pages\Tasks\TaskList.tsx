import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Statistic,
  Progress,
  Avatar,
  Tooltip,
  Dropdown,
  Modal,
  Form,
  message,
  Empty,
  Tabs,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckOutlined,
  ClockCircleOutlined,
  UserOutlined,
  CalendarOutlined,
  FlagOutlined,
  MoreOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { Task, TaskStatus, TaskPriority } from '@/types/api';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

const TaskList: React.FC = () => {
  const navigate = useNavigate();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [searchText, setSearchText] = useState('');
  const [filters, setFilters] = useState({
    status: undefined as TaskStatus | undefined,
    priority: undefined as TaskPriority | undefined,
    assignee: undefined as string | undefined,
    dateRange: undefined as [string, string] | undefined,
  });
  const [activeTab, setActiveTab] = useState('all');
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 模拟数据
  useEffect(() => {
    setTasks([
      {
        id: 1,
        meetingId: 1,
        title: '完成产品需求文档',
        description: '根据会议讨论结果，完善产品需求文档的细节部分',
        status: 'PENDING',
        priority: 'HIGH',
        assigneeName: '张三',
        assigneeId: 1,
        dueDate: '2024-01-15T18:00:00Z',
        progress: 30,
        sourceTime: 1800,
        createdAt: '2024-01-10T10:00:00Z',
        updatedAt: '2024-01-10T10:00:00Z',
      },
      {
        id: 2,
        meetingId: 1,
        title: '技术方案评审',
        description: '组织技术团队对新功能的技术方案进行评审',
        status: 'IN_PROGRESS',
        priority: 'MEDIUM',
        assigneeName: '李四',
        assigneeId: 2,
        dueDate: '2024-01-20T16:00:00Z',
        progress: 60,
        sourceTime: 2400,
        createdAt: '2024-01-10T10:00:00Z',
        updatedAt: '2024-01-12T14:00:00Z',
      },
      {
        id: 3,
        meetingId: 2,
        title: '用户调研报告',
        description: '整理用户调研数据，形成分析报告',
        status: 'COMPLETED',
        priority: 'LOW',
        assigneeName: '王五',
        assigneeId: 3,
        dueDate: '2024-01-12T17:00:00Z',
        progress: 100,
        sourceTime: 3600,
        createdAt: '2024-01-08T09:00:00Z',
        updatedAt: '2024-01-12T17:00:00Z',
      },
    ]);
  }, []);

  // 获取状态标签
  const getStatusTag = (status: TaskStatus) => {
    const statusConfig = {
      PENDING: { color: 'default', text: '待处理' },
      IN_PROGRESS: { color: 'processing', text: '进行中' },
      COMPLETED: { color: 'success', text: '已完成' },
      CANCELLED: { color: 'error', text: '已取消' },
    };
    const config = statusConfig[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取优先级标签
  const getPriorityTag = (priority: TaskPriority) => {
    const priorityConfig = {
      LOW: { color: 'green', text: '低', icon: '🟢' },
      MEDIUM: { color: 'orange', text: '中', icon: '🟡' },
      HIGH: { color: 'red', text: '高', icon: '🔴' },
    };
    const config = priorityConfig[priority];
    return (
      <Tag color={config.color} icon={<span>{config.icon}</span>}>
        {config.text}
      </Tag>
    );
  };

  // 计算剩余时间
  const getRemainingTime = (dueDate: string) => {
    const now = dayjs();
    const due = dayjs(dueDate);
    const diff = due.diff(now, 'day');
    
    if (diff < 0) {
      return <span style={{ color: '#ff4d4f' }}>已逾期 {Math.abs(diff)} 天</span>;
    } else if (diff === 0) {
      return <span style={{ color: '#faad14' }}>今天到期</span>;
    } else if (diff <= 3) {
      return <span style={{ color: '#faad14' }}>还有 {diff} 天</span>;
    } else {
      return <span style={{ color: '#52c41a' }}>还有 {diff} 天</span>;
    }
  };

  // 处理任务编辑
  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    form.setFieldsValue({
      ...task,
      dueDate: task.dueDate ? dayjs(task.dueDate) : null,
    });
    setIsModalVisible(true);
  };

  // 处理任务更新
  const handleUpdateTask = async (values: any) => {
    try {
      // 这里应该调用API更新任务
      const updatedTask = {
        ...editingTask,
        ...values,
        dueDate: values.dueDate?.toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      setTasks(prev => prev.map(task => 
        task.id === editingTask?.id ? updatedTask : task
      ));
      
      setIsModalVisible(false);
      setEditingTask(null);
      form.resetFields();
      message.success('任务更新成功');
    } catch (error) {
      message.error('任务更新失败');
    }
  };

  // 处理任务删除
  const handleDeleteTask = (taskId: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个任务吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        setTasks(prev => prev.filter(task => task.id !== taskId));
        message.success('任务删除成功');
      },
    });
  };

  // 处理任务状态更新
  const handleStatusChange = (taskId: number, status: TaskStatus) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { 
            ...task, 
            status, 
            progress: status === 'COMPLETED' ? 100 : task.progress,
            updatedAt: new Date().toISOString() 
          }
        : task
    ));
    message.success('任务状态更新成功');
  };

  // 筛选任务
  const getFilteredTasks = () => {
    let filtered = tasks;

    // 按标签页筛选
    if (activeTab !== 'all') {
      filtered = filtered.filter(task => task.status === activeTab.toUpperCase());
    }

    // 按搜索文本筛选
    if (searchText) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(searchText.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchText.toLowerCase()) ||
        task.assigneeName?.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // 按其他条件筛选
    if (filters.status) {
      filtered = filtered.filter(task => task.status === filters.status);
    }
    if (filters.priority) {
      filtered = filtered.filter(task => task.priority === filters.priority);
    }
    if (filters.assignee) {
      filtered = filtered.filter(task => task.assigneeName?.includes(filters.assignee!));
    }

    return filtered;
  };

  const filteredTasks = getFilteredTasks();

  // 表格列配置
  const columns: ColumnsType<Task> = [
    {
      title: '任务标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          {record.description && (
            <div style={{ fontSize: 12, color: '#999', marginTop: 4 }}>
              {record.description}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: getStatusTag,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: getPriorityTag,
    },
    {
      title: '负责人',
      dataIndex: 'assigneeName',
      key: 'assigneeName',
      width: 120,
      render: (name) => name ? (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <span>{name}</span>
        </Space>
      ) : '-',
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress) => (
        <Progress 
          percent={progress} 
          size="small" 
          status={progress === 100 ? 'success' : 'active'}
        />
      ),
    },
    {
      title: '截止时间',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 150,
      render: (date) => date ? (
        <div>
          <div>{dayjs(date).format('MM-DD HH:mm')}</div>
          <div style={{ fontSize: 12 }}>
            {getRemainingTime(date)}
          </div>
        </div>
      ) : '-',
    },
    {
      title: '来源',
      dataIndex: 'sourceTime',
      key: 'sourceTime',
      width: 100,
      render: (time, record) => (
        <Tooltip title="点击查看会议详情">
          <Button 
            type="link" 
            size="small"
            icon={<PlayCircleOutlined />}
            onClick={() => navigate(`/meetings/${record.meetingId}`)}
          >
            {time ? `${Math.floor(time / 60)}:${(time % 60).toString().padStart(2, '0')}` : '手动'}
          </Button>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => {
        const menuItems = [
          {
            key: 'edit',
            icon: <EditOutlined />,
            label: '编辑',
            onClick: () => handleEditTask(record),
          },
          {
            key: 'complete',
            icon: <CheckOutlined />,
            label: '标记完成',
            disabled: record.status === 'COMPLETED',
            onClick: () => handleStatusChange(record.id, 'COMPLETED'),
          },
          {
            type: 'divider' as const,
          },
          {
            key: 'delete',
            icon: <DeleteOutlined />,
            label: '删除',
            danger: true,
            onClick: () => handleDeleteTask(record.id),
          },
        ];

        return (
          <Dropdown menu={{ items: menuItems }} trigger={['click']}>
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        );
      },
    },
  ];

  // 统计数据
  const stats = {
    total: tasks.length,
    pending: tasks.filter(t => t.status === 'PENDING').length,
    inProgress: tasks.filter(t => t.status === 'IN_PROGRESS').length,
    completed: tasks.filter(t => t.status === 'COMPLETED').length,
    overdue: tasks.filter(t => t.dueDate && dayjs(t.dueDate).isBefore(dayjs())).length,
  };

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <h2 style={{ margin: 0 }}>任务管理</h2>
          </Col>
          <Col>
            <Button type="primary" icon={<PlusOutlined />}>
              创建任务
            </Button>
          </Col>
        </Row>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总任务数"
              value={stats.total}
              prefix={<CheckOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待处理"
              value={stats.pending}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="进行中"
              value={stats.inProgress}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completed}
              prefix={<CheckOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Input.Search
              placeholder="搜索任务标题、描述、负责人..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col>
            <Select
              placeholder="状态"
              allowClear
              style={{ width: 120 }}
              value={filters.status}
              onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
            >
              <Option value="PENDING">待处理</Option>
              <Option value="IN_PROGRESS">进行中</Option>
              <Option value="COMPLETED">已完成</Option>
              <Option value="CANCELLED">已取消</Option>
            </Select>
          </Col>
          <Col>
            <Select
              placeholder="优先级"
              allowClear
              style={{ width: 120 }}
              value={filters.priority}
              onChange={(value) => setFilters(prev => ({ ...prev, priority: value }))}
            >
              <Option value="HIGH">高</Option>
              <Option value="MEDIUM">中</Option>
              <Option value="LOW">低</Option>
            </Select>
          </Col>
          <Col>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              onChange={(dates) => {
                if (dates) {
                  setFilters(prev => ({ 
                    ...prev, 
                    dateRange: [dates[0]!.toISOString(), dates[1]!.toISOString()] 
                  }));
                } else {
                  setFilters(prev => ({ ...prev, dateRange: undefined }));
                }
              }}
            />
          </Col>
        </Row>
      </Card>

      {/* 任务列表 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={`全部 (${stats.total})`} key="all" />
          <TabPane tab={`待处理 (${stats.pending})`} key="pending" />
          <TabPane tab={`进行中 (${stats.inProgress})`} key="in_progress" />
          <TabPane tab={`已完成 (${stats.completed})`} key="completed" />
        </Tabs>

        <Table
          columns={columns}
          dataSource={filteredTasks}
          rowKey="id"
          loading={isLoading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
          locale={{
            emptyText: (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无任务"
              >
                <Button type="primary" icon={<PlusOutlined />}>
                  创建第一个任务
                </Button>
              </Empty>
            ),
          }}
        />
      </Card>

      {/* 编辑任务模态框 */}
      <Modal
        title="编辑任务"
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingTask(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpdateTask}
        >
          <Form.Item
            name="title"
            label="任务标题"
            rules={[{ required: true, message: '请输入任务标题' }]}
          >
            <Input placeholder="请输入任务标题" />
          </Form.Item>

          <Form.Item name="description" label="任务描述">
            <Input.TextArea rows={3} placeholder="请输入任务描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="status" label="状态">
                <Select>
                  <Option value="PENDING">待处理</Option>
                  <Option value="IN_PROGRESS">进行中</Option>
                  <Option value="COMPLETED">已完成</Option>
                  <Option value="CANCELLED">已取消</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="priority" label="优先级">
                <Select>
                  <Option value="LOW">低</Option>
                  <Option value="MEDIUM">中</Option>
                  <Option value="HIGH">高</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="assigneeName" label="负责人">
                <Input placeholder="请输入负责人姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="dueDate" label="截止时间">
                <DatePicker 
                  showTime 
                  style={{ width: '100%' }}
                  placeholder="选择截止时间"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="progress" label="完成进度">
            <Input 
              type="number" 
              min={0} 
              max={100} 
              suffix="%" 
              placeholder="0-100"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TaskList;
