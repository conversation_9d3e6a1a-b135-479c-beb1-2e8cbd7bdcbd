import { Router } from 'express';
import { AuthController } from '../controllers/auth.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = Router();
const authController = new AuthController();

// 公开路由（不需要认证）
router.post('/register', authController.register);
router.post('/login', authController.login);

// 需要认证的路由
router.post('/logout', authMiddleware, authController.logout);
router.post('/refresh', authMiddleware, authController.refreshToken);
router.get('/profile', authMiddleware, authController.getProfile);
router.put('/profile', authMiddleware, authController.updateProfile);
router.post('/change-password', authMiddleware, authController.changePassword);

export default router;
